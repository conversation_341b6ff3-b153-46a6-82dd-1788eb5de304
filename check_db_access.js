#!/usr/bin/env node

import { query } from './src/config/database.js';

async function checkDatabaseAccess() {
    try {
        console.log('🔍 Checking Database Access Configuration');
        console.log('==========================================');
        
        // Check user info
        console.log('\n1. User Information:');
        const userResult = await query(`
            SELECT u.email, u.tier_id, t.name as tier_name, t.allowed_streams as tier_allowed_streams
            FROM users u
            LEFT JOIN access_tiers t ON u.tier_id = t.id
            WHERE u.api_key = $1
        `, ['test_free_api_key_12345']);
        
        if (userResult.rows.length > 0) {
            const user = userResult.rows[0];
            console.log('   Email:', user.email);
            console.log('   Tier ID:', user.tier_id);
            console.log('   Tier Name:', user.tier_name);
            console.log('   Tier Allowed Streams:', user.tier_allowed_streams);
        } else {
            console.log('   ❌ User not found');
            return;
        }
        
        // Check kol-feed stream info
        console.log('\n2. KOL Feed Stream Information:');
        const streamResult = await query(`
            SELECT stream_name, required_tier_id, is_active, description
            FROM stream_definitions 
            WHERE stream_name = $1
        `, ['kol-feed']);
        
        if (streamResult.rows.length > 0) {
            const stream = streamResult.rows[0];
            console.log('   Stream Name:', stream.stream_name);
            console.log('   Required Tier ID:', stream.required_tier_id);
            console.log('   Is Active:', stream.is_active);
            console.log('   Description:', stream.description);
        } else {
            console.log('   ❌ kol-feed stream not found in database');
        }
        
        // Check all tiers
        console.log('\n3. All Access Tiers:');
        const tierResult = await query(`
            SELECT id, name, allowed_streams, max_credits_per_month
            FROM access_tiers 
            ORDER BY id
        `);
        
        tierResult.rows.forEach(tier => {
            console.log(`   Tier ${tier.id}: ${tier.name}`);
            console.log(`     Allowed Streams: ${tier.allowed_streams}`);
            console.log(`     Max Credits: ${tier.max_credits_per_month}`);
        });
        
        // Check all streams
        console.log('\n4. All Stream Definitions:');
        const allStreamsResult = await query(`
            SELECT stream_name, required_tier_id, is_active
            FROM stream_definitions 
            ORDER BY required_tier_id, stream_name
        `);
        
        allStreamsResult.rows.forEach(stream => {
            console.log(`   ${stream.stream_name}: Tier ${stream.required_tier_id} required, Active: ${stream.is_active}`);
        });
        
        process.exit(0);
    } catch (error) {
        console.error('❌ Error:', error);
        process.exit(1);
    }
}

checkDatabaseAccess();
