import { query, testConnection } from '../src/config/database.js';

async function checkAndAddSolanaTrackerStreams() {
    try {
        console.log('🔍 Checking current stream_definitions table...');
        
        // Test database connection
        const connected = await testConnection();
        if (!connected) {
            throw new Error('Database connection failed');
        }

        // Check current table structure
        console.log('\n📋 Current table structure:');
        const tableInfo = await query(`
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'stream_definitions' 
            ORDER BY ordinal_position;
        `);
        
        console.log('Columns in stream_definitions:');
        tableInfo.rows.forEach(col => {
            console.log(`  - ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
        });

        // Check current streams
        console.log('\n📊 Current streams in database:');
        const currentStreams = await query(`
            SELECT id, stream_name, description, required_tier_id, credits_per_message, max_subscribers, is_active
            FROM stream_definitions 
            ORDER BY stream_name;
        `);
        
        console.log(`Found ${currentStreams.rows.length} existing streams:`);
        currentStreams.rows.forEach(stream => {
            console.log(`  - ${stream.stream_name}: ${stream.description} (tier: ${stream.required_tier_id}, credits: ${stream.credits_per_message})`);
        });

        // Check if SolanaTracker streams already exist
        const solanaStreams = [
            'tokens-launched', 'tokens-graduating', 'tokens-graduated',
            'pool-changes', 'token-transactions', 'price-updates', 
            'wallet-transactions', 'token-metadata', 'token-holders', 'token-changes'
        ];

        const existingStreams = currentStreams.rows.map(s => s.stream_name);
        const missingStreams = solanaStreams.filter(s => !existingStreams.includes(s));

        if (missingStreams.length === 0) {
            console.log('\n✅ All SolanaTracker streams already exist in database!');
            return;
        }

        console.log(`\n🔧 Need to add ${missingStreams.length} missing streams:`, missingStreams);

        // Get the tier ID for premium tier
        const tierQuery = await query(`
            SELECT id FROM access_tiers WHERE name = 'premium' OR name = 'basic' ORDER BY id LIMIT 1;
        `);
        
        const premiumTierId = tierQuery.rows[0]?.id || 3; // Default to 3 if not found
        console.log(`Using tier ID: ${premiumTierId} for premium access`);

        // Add missing streams
        const streamsToAdd = [
            {
                name: 'tokens-launched',
                description: 'Latest tokens and pools launched on Solana blockchain via SolanaTracker API',
                tier: premiumTierId,
                credits: 0
            },
            {
                name: 'tokens-graduating',
                description: 'Tokens approaching graduation on Pump.fun/Moonshot via SolanaTracker API',
                tier: premiumTierId,
                credits: 0
            },
            {
                name: 'tokens-graduated',
                description: 'Recently graduated tokens from Pump.fun/Moonshot via SolanaTracker API',
                tier: premiumTierId,
                credits: 0
            },
            {
                name: 'pool-changes',
                description: 'Real-time updates about changes in specific pools via SolanaTracker API',
                tier: premiumTierId,
                credits: 0
            },
            {
                name: 'token-transactions',
                description: 'Real-time transactions for specific tokens via SolanaTracker API',
                tier: premiumTierId,
                credits: 0
            },
            {
                name: 'price-updates',
                description: 'Real-time price updates for specific pools or tokens via SolanaTracker API',
                tier: premiumTierId,
                credits: 0
            },
            {
                name: 'wallet-transactions',
                description: 'Real-time transaction updates for specific wallets via SolanaTracker API',
                tier: premiumTierId,
                credits: 0
            },
            {
                name: 'token-metadata',
                description: 'Complete token metadata updates via SolanaTracker API (BETA)',
                tier: premiumTierId,
                credits: 0
            },
            {
                name: 'token-holders',
                description: 'Token holder count changes via SolanaTracker API (BETA)',
                tier: premiumTierId,
                credits: 0
            },
            {
                name: 'token-changes',
                description: 'All updates from any pool for specific tokens via SolanaTracker API',
                tier: premiumTierId,
                credits: 0
            }
        ];

        console.log('\n🚀 Adding SolanaTracker streams to database...');

        for (const stream of streamsToAdd) {
            if (missingStreams.includes(stream.name)) {
                try {
                    const result = await query(`
                        INSERT INTO stream_definitions (
                            stream_name,
                            description,
                            required_tier_id,
                            credits_per_message,
                            max_subscribers,
                            is_active
                        ) VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (stream_name) DO UPDATE SET
                            description = EXCLUDED.description,
                            required_tier_id = EXCLUDED.required_tier_id,
                            credits_per_message = EXCLUDED.credits_per_message,
                            is_active = EXCLUDED.is_active;
                    `, [
                        stream.name,
                        stream.description,
                        stream.tier,
                        stream.credits,
                        1000, // max_subscribers
                        true  // is_active
                    ]);

                    console.log(`✅ Added stream: ${stream.name}`);
                } catch (error) {
                    console.error(`❌ Failed to add stream ${stream.name}:`, error.message);
                }
            }
        }

        // Verify the additions
        console.log('\n🔍 Verifying added streams...');
        const verifyQuery = await query(`
            SELECT stream_name, description, required_tier_id, credits_per_message, is_active
            FROM stream_definitions 
            WHERE stream_name = ANY($1)
            ORDER BY stream_name;
        `, [solanaStreams]);

        console.log(`\n✅ SolanaTracker streams in database (${verifyQuery.rows.length}/10):`);
        verifyQuery.rows.forEach(stream => {
            console.log(`  - ${stream.stream_name}: tier ${stream.required_tier_id}, ${stream.credits_per_message} credits`);
        });

        if (verifyQuery.rows.length === 10) {
            console.log('\n🎉 All SolanaTracker streams successfully added to database!');
        } else {
            console.log(`\n⚠️  Only ${verifyQuery.rows.length}/10 streams found. Some may have failed to add.`);
        }

    } catch (error) {
        console.error('❌ Error checking/adding streams:', error);
        process.exit(1);
    }
}

// Run the script
checkAndAddSolanaTrackerStreams();
