#!/usr/bin/env node

import WebSocket from 'ws';

async function testWebSocketAuth() {
    console.log('🔍 Testing WebSocket Authentication');
    console.log('===================================');
    
    console.log('🔌 Testing connection WITHOUT API key...');
    
    // Test 1: Connection without API key
    const ws1 = new WebSocket('ws://localhost:3001/ws');
    
    ws1.on('open', () => {
        console.log('❌ Connection opened without API key (should fail)');
    });
    
    ws1.on('close', (code, reason) => {
        console.log(`✅ Connection closed without API key: Code ${code}, Reason: ${reason}`);
    });
    
    ws1.on('error', (error) => {
        console.log(`✅ Connection error without API key: ${error.message}`);
    });
    
    // Wait a bit
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log('\n🔌 Testing connection WITH API key...');
    
    // Test 2: Connection with API key
    const ws2 = new WebSocket('ws://localhost:3001/ws?apiKey=test_free_api_key_12345');
    
    ws2.on('open', () => {
        console.log('✅ Connection opened with API key');
    });
    
    ws2.on('message', (data) => {
        const message = JSON.parse(data);
        console.log('📨 Received message:', message);
        
        if (message.type === 'connected') {
            console.log('✅ Successfully authenticated and connected');
            
            // Try to subscribe to kol-feed
            console.log('📤 Subscribing to kol-feed...');
            ws2.send(JSON.stringify({
                type: 'subscribe',
                payload: { stream: 'kol-feed' }
            }));
        } else if (message.type === 'subscribed') {
            console.log(`✅ Successfully subscribed to ${message.stream}`);
        } else if (message.type === 'error') {
            console.log(`❌ Error: ${message.error}`);
        }
    });
    
    ws2.on('close', (code, reason) => {
        console.log(`❌ Connection closed with API key: Code ${code}, Reason: ${reason}`);
    });
    
    ws2.on('error', (error) => {
        console.log(`❌ Connection error with API key: ${error.message}`);
    });
    
    // Wait for connection and subscription
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    console.log('\n🔌 Closing connections...');
    ws1.close();
    ws2.close();
    
    process.exit(0);
}

testWebSocketAuth().catch(console.error);
