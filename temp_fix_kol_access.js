#!/usr/bin/env node

import { query } from './src/config/database.js';

async function tempFixKolAccess() {
    try {
        console.log('🔧 Temporarily lowering kol-feed stream requirement to tier 1...');
        
        // Lower the kol-feed stream requirement to tier 1 (free tier)
        const result = await query(
            'UPDATE stream_definitions SET required_tier_id = 1 WHERE stream_name = $1 RETURNING stream_name, required_tier_id', 
            ['kol-feed']
        );
        
        if (result.rows.length > 0) {
            const stream = result.rows[0];
            console.log('✅ Stream requirement updated:');
            console.log('   Stream:', stream.stream_name);
            console.log('   New Required Tier ID:', stream.required_tier_id);
            console.log('   This allows free tier users to access kol-feed for testing');
        } else {
            console.log('❌ Stream not found');
        }
        
        process.exit(0);
    } catch (error) {
        console.error('❌ Error:', error);
        process.exit(1);
    }
}

tempFixKolAccess();
