#!/usr/bin/env node

/**
 * KOL Feed Stress Test
 * 
 * This test creates 100 user accounts and simulates them all connecting to the KOL feed stream
 * to stress test the database and measure performance under high load.
 */

import { WebSocket } from 'ws';
import { query, testConnection, getPoolStats } from '../src/config/database.js';
import { redisCreditTracker } from '../src/services/RedisCreditTracker.js';
import bcrypt from 'bcryptjs';
import crypto from 'crypto';
import dotenv from 'dotenv';

dotenv.config();

const BASE_URL = 'ws://localhost:3001';
const NUM_USERS = 50; // Back to full stress test
const TEST_DURATION = 300000; // 30 seconds test
const STATS_INTERVAL = 5000; // Report stats every 5 seconds
const STREAM_ENDPOINT = 'kol-feed';

// Test configuration
const TEST_CONFIG = {
    numUsers: NUM_USERS,
    testDuration: TEST_DURATION,
    statsInterval: STATS_INTERVAL,
    connectionDelay: 100, // Delay between connections (ms)
    messageTimeout: 30000, // Timeout for receiving messages
    tier: 'basic' // Tier for test users (basic = 2 credits per message)
};

// Global test state
const testState = {
    users: [],
    connections: [],
    stats: {
        connectionsOpened: 0,
        connectionsFailed: 0,
        messagesReceived: 0,
        creditsConsumed: 0,
        errors: 0,
        startTime: null,
        endTime: null
    },
    dbStats: {
        initial: null,
        current: null,
        snapshots: []
    }
};

console.log('🔥 KOL Feed Stress Test');
console.log('========================');
console.log(`👥 Users: ${TEST_CONFIG.numUsers}`);
console.log(`⏱️  Duration: ${TEST_CONFIG.testDuration / 1000}s`);
console.log(`📊 Stats interval: ${TEST_CONFIG.statsInterval / 1000}s`);
console.log('');

/**
 * Create test users in the database
 */
async function createTestUsers() {
    console.log('🔧 Creating test users...');
    
    const users = [];
    const passwordHash = await bcrypt.hash('test123', 10);
    
    // Get basic tier ID
    const tierResult = await query("SELECT id FROM access_tiers WHERE name = 'basic'");
    const tierId = tierResult.rows[0]?.id;
    
    if (!tierId) {
        throw new Error('Basic tier not found in database');
    }
    
    // Create users in batches to avoid overwhelming the database
    const batchSize = 10;
    for (let i = 0; i < NUM_USERS; i += batchSize) {
        const batch = [];
        
        for (let j = 0; j < batchSize && (i + j) < NUM_USERS; j++) {
            const userIndex = i + j + 1;
            const user = {
                email: `stress-test-user-${userIndex}@example.com`,
                apiKey: `stress_test_api_key_${userIndex}_${crypto.randomBytes(16).toString('hex')}`,
                tierId: tierId,
                credits: 1000000 // 1M credits for testing
            };
            
            batch.push(user);
            users.push(user);
        }
        
        // Insert batch
        const values = batch.map((user, idx) => {
            const baseIdx = idx * 5; // 5 parameters per user
            return `($${baseIdx + 1}, $${baseIdx + 2}, $${baseIdx + 3}, $${baseIdx + 4}, $${baseIdx + 5})`;
        }).join(', ');

        const params = batch.flatMap(user => [
            user.email,
            passwordHash,
            user.apiKey,
            user.tierId,
            user.credits
        ]);
        
        await query(`
            INSERT INTO users (email, password_hash, api_key, tier_id, credits_remaining)
            VALUES ${values}
            ON CONFLICT (email) DO UPDATE SET
                api_key = EXCLUDED.api_key,
                credits_remaining = EXCLUDED.credits_remaining
        `, params);
        
        console.log(`   Created users ${i + 1}-${Math.min(i + batchSize, NUM_USERS)}`);
    }
    
    testState.users = users;
    console.log(`✅ Created ${users.length} test users`);

    // Reload Redis credit data to include new test users
    if (process.env.NODE_ENV === 'production') {
        console.log('🔄 Reloading Redis credit data for new test users...');
        try {
            // Load each test user into Redis
            for (const user of users) {
                // Get the user ID from database
                const userResult = await query('SELECT id FROM users WHERE api_key = $1', [user.apiKey]);
                if (userResult.rows.length > 0) {
                    const userId = userResult.rows[0].id;
                    await redisCreditTracker.loadUserCreditsFromDB(userId);
                }
            }
            console.log('✅ Redis credit data reloaded for test users');
        } catch (error) {
            console.error('⚠️ Failed to reload Redis credit data:', error);
            console.log('   Test will continue with database fallback');
        }
    }
}

/**
 * Get database performance statistics
 */
async function getDatabaseStats() {
    try {
        const poolStats = getPoolStats();
        
        const connectionStats = await query(`
            SELECT 
                count(*) as total_connections,
                count(*) FILTER (WHERE state = 'active') as active_connections,
                count(*) FILTER (WHERE state = 'idle') as idle_connections
            FROM pg_stat_activity 
            WHERE datname = current_database()
        `);
        
        const usageStats = await query(`
            SELECT 
                count(*) as total_usage_logs,
                count(*) FILTER (WHERE created_at > NOW() - INTERVAL '1 minute') as recent_logs,
                COALESCE(sum(credits_consumed) FILTER (WHERE created_at > NOW() - INTERVAL '1 minute'), 0) as recent_credits
            FROM api_usage_logs
        `);
        
        const userStats = await query(`
            SELECT 
                count(*) as total_users,
                avg(credits_remaining) as avg_credits_remaining,
                sum(credits_used_this_month) as total_credits_used
            FROM users 
            WHERE email LIKE 'stress-test-user-%'
        `);
        
        return {
            timestamp: new Date(),
            pool: poolStats,
            connections: connectionStats.rows[0],
            usage: usageStats.rows[0],
            users: userStats.rows[0]
        };
    } catch (error) {
        console.error('❌ Error getting database stats:', error);
        return null;
    }
}

/**
 * Create WebSocket connection for a user
 */
async function createConnection(user, index) {
    return new Promise((resolve) => {
        const wsUrl = `${BASE_URL}/ws?apiKey=${user.apiKey}`;
        const ws = new WebSocket(wsUrl);
        
        const connectionData = {
            user,
            ws,
            index,
            connected: false,
            subscribed: false,
            messagesReceived: 0,
            lastMessage: null,
            errors: []
        };
        
        const timeout = setTimeout(() => {
            if (!connectionData.connected) {
                ws.terminate();
                testState.stats.connectionsFailed++;
                resolve(connectionData);
            }
        }, 10000);
        
        ws.on('open', () => {
            clearTimeout(timeout);
            connectionData.connected = true;
            testState.stats.connectionsOpened++;

            // Wait a bit for the connection to be fully established, then subscribe
            setTimeout(() => {
                const subscribeMessage = {
                    type: 'subscribe',
                    payload: { stream: STREAM_ENDPOINT }
                };

                ws.send(JSON.stringify(subscribeMessage));
                if (index < 10) {
                    console.log(`   User ${index + 1}: Sent subscription message:`, JSON.stringify(subscribeMessage));
                }
                console.log(`   User ${index + 1}: Connected and subscribed`);
            }, 500); // 500ms delay

            resolve(connectionData);
        });
        
        ws.on('message', (data) => {
            try {
                const message = JSON.parse(data.toString());
                connectionData.lastMessage = message;

                // Log all messages for debugging (first 10 users only)
                if (index < 10) {
                    console.log(`   User ${index + 1} received message:`, JSON.stringify(message));
                }

                if (message.type === 'stream_data' && message.stream === STREAM_ENDPOINT) {
                    connectionData.messagesReceived++;
                    testState.stats.messagesReceived++;
                    testState.stats.creditsConsumed += 2; // KOL feed costs 2 credits per message

                    // Log when we receive actual stream data
                    if (testState.stats.messagesReceived <= 5) {
                        console.log(`🎉 Received KOL feed message #${testState.stats.messagesReceived} from user ${index + 1}`);
                    }
                } else if (message.type === 'subscribed') {
                    connectionData.subscribed = true;
                    console.log(`   User ${index + 1}: Subscription confirmed for ${message.stream || 'unknown stream'}`);
                } else if (message.type === 'error') {
                    connectionData.errors.push(message);
                    testState.stats.errors++;
                    console.log(`   User ${index + 1}: Error - ${message.error || message.message}`);
                } else if (message.type === 'connected') {
                    console.log(`   User ${index + 1}: Connection confirmed`);
                } else {
                    // Log other message types for debugging
                    if (index < 10) {
                        console.log(`   User ${index + 1}: Other message type: ${message.type}`);
                    }
                }
            } catch (error) {
                connectionData.errors.push({ type: 'parse_error', error: error.message });
                testState.stats.errors++;
                console.log(`   User ${index + 1}: Parse error - ${error.message}`);
            }
        });
        
        ws.on('error', (error) => {
            connectionData.errors.push({ type: 'ws_error', error: error.message });
            testState.stats.errors++;
            if (!connectionData.connected) {
                clearTimeout(timeout);
                testState.stats.connectionsFailed++;
                resolve(connectionData);
            }
        });
        
        ws.on('close', (code, reason) => {
            if (connectionData.connected) {
                console.log(`   User ${index + 1}: Disconnected (${code}: ${reason})`);
            }
        });
    });
}

/**
 * Create all WebSocket connections
 */
async function createConnections() {
    console.log('🔌 Creating WebSocket connections...');
    
    const connections = [];
    
    for (let i = 0; i < testState.users.length; i++) {
        const user = testState.users[i];
        
        try {
            const connection = await createConnection(user, i);
            connections.push(connection);
            
            // Small delay between connections to avoid overwhelming the server
            if (i < testState.users.length - 1) {
                await new Promise(resolve => setTimeout(resolve, TEST_CONFIG.connectionDelay));
            }
        } catch (error) {
            console.error(`❌ Failed to create connection for user ${i + 1}:`, error);
            testState.stats.connectionsFailed++;
        }
    }
    
    testState.connections = connections;
    console.log(`✅ Created ${connections.length} connections`);
    console.log(`   Successful: ${testState.stats.connectionsOpened}`);
    console.log(`   Failed: ${testState.stats.connectionsFailed}`);
}

/**
 * Print current statistics
 */
function printStats() {
    const elapsed = Date.now() - testState.stats.startTime;
    const elapsedSeconds = Math.floor(elapsed / 1000);
    
    console.log(`\n📊 Stats at ${elapsedSeconds}s:`);
    console.log(`   Connections: ${testState.stats.connectionsOpened}/${NUM_USERS} successful`);
    console.log(`   Messages received: ${testState.stats.messagesReceived}`);
    console.log(`   Credits consumed: ${testState.stats.creditsConsumed}`);
    console.log(`   Errors: ${testState.stats.errors}`);
    
    if (testState.dbStats.current) {
        const db = testState.dbStats.current;
        console.log(`   DB Pool: ${db.pool.totalCount}/${db.pool.maxConnections} (${db.pool.idleCount} idle)`);
        console.log(`   DB Connections: ${db.connections.active_connections} active, ${db.connections.total_connections} total`);
        console.log(`   Recent logs: ${db.usage.recent_logs}`);
    }
}

/**
 * Monitor database performance
 */
async function monitorDatabase() {
    const stats = await getDatabaseStats();
    if (stats) {
        testState.dbStats.current = stats;
        testState.dbStats.snapshots.push(stats);
    }
}

/**
 * Run the stress test
 */
async function runStressTest() {
    try {
        // Test database connection
        console.log('🔍 Testing database connection...');
        const dbConnected = await testConnection();
        if (!dbConnected) {
            throw new Error('Database connection failed');
        }
        
        // Get initial database stats
        testState.dbStats.initial = await getDatabaseStats();
        
        // Create test users
        await createTestUsers();
        
        // Create connections
        await createConnections();
        
        // Start monitoring
        testState.stats.startTime = Date.now();
        
        console.log(`\n🚀 Starting ${TEST_CONFIG.testDuration / 1000}s stress test...`);

        // Debug: Check if users have credits in Redis (production mode only)
        if (process.env.NODE_ENV === 'production') {
            console.log('🔍 Checking Redis credit data for first 5 test users...');
            for (let i = 0; i < Math.min(5, testState.users.length); i++) {
                const user = testState.users[i];
                const userResult = await query('SELECT id FROM users WHERE api_key = $1', [user.apiKey]);
                if (userResult.rows.length > 0) {
                    const userId = userResult.rows[0].id;
                    const hasCredits = await redisCreditTracker.checkCredits(userId, 2);
                    console.log(`   User ${i + 1} (${userId}): ${hasCredits ? '✅ Has credits' : '❌ No credits'}`);
                }
            }
        }
        
        // Set up periodic stats reporting
        const statsInterval = setInterval(() => {
            monitorDatabase();
            printStats();
        }, TEST_CONFIG.statsInterval);
        
        // Wait for test duration
        await new Promise(resolve => setTimeout(resolve, TEST_CONFIG.testDuration));
        
        // Stop monitoring
        clearInterval(statsInterval);
        testState.stats.endTime = Date.now();
        
        // Final stats
        await monitorDatabase();
        console.log('\n🏁 Test completed!');
        printFinalReport();
        
    } catch (error) {
        console.error('❌ Stress test failed:', error);
        process.exit(1);
    } finally {
        // Cleanup
        await cleanup();
    }
}

/**
 * Print final test report
 */
function printFinalReport() {
    const duration = testState.stats.endTime - testState.stats.startTime;
    const durationSeconds = duration / 1000;
    
    console.log('\n📋 FINAL REPORT');
    console.log('================');
    console.log(`Test Duration: ${durationSeconds.toFixed(1)}s`);
    console.log(`Users Created: ${testState.users.length}`);
    console.log(`Successful Connections: ${testState.stats.connectionsOpened}/${NUM_USERS} (${(testState.stats.connectionsOpened / NUM_USERS * 100).toFixed(1)}%)`);
    console.log(`Failed Connections: ${testState.stats.connectionsFailed}`);
    console.log(`Total Messages: ${testState.stats.messagesReceived}`);
    console.log(`Messages/Second: ${(testState.stats.messagesReceived / durationSeconds).toFixed(2)}`);
    console.log(`Credits Consumed: ${testState.stats.creditsConsumed}`);
    console.log(`Total Errors: ${testState.stats.errors}`);
    
    if (testState.dbStats.initial && testState.dbStats.current) {
        const initial = testState.dbStats.initial;
        const final = testState.dbStats.current;
        
        console.log('\n📊 DATABASE PERFORMANCE');
        console.log('========================');
        console.log(`Pool Usage: ${final.pool.totalCount}/${final.pool.maxConnections} connections`);
        console.log(`Active DB Connections: ${final.connections.active_connections}`);
        console.log(`New Usage Logs: ${parseInt(final.usage.total_usage_logs) - parseInt(initial.usage.total_usage_logs)}`);
        console.log(`Credits from DB: ${parseInt(final.usage.recent_credits)}`);
        
        // Performance assessment
        const poolUtilization = final.pool.totalCount / final.pool.maxConnections;
        console.log('\n🎯 PERFORMANCE ASSESSMENT');
        console.log('==========================');
        
        if (poolUtilization > 0.8) {
            console.log('⚠️  HIGH database pool utilization - consider increasing pool size');
        } else if (poolUtilization > 0.5) {
            console.log('⚡ MEDIUM database pool utilization - performance is acceptable');
        } else {
            console.log('✅ LOW database pool utilization - excellent performance');
        }
        
        if (testState.stats.errors > testState.stats.messagesReceived * 0.1) {
            console.log('❌ HIGH error rate - system may be overloaded');
        } else if (testState.stats.errors > 0) {
            console.log('⚠️  Some errors occurred - monitor for patterns');
        } else {
            console.log('✅ No errors - system handled load well');
        }
    }
}

/**
 * Cleanup test resources
 */
async function cleanup() {
    console.log('\n🧹 Cleaning up...');
    
    // Close all WebSocket connections
    testState.connections.forEach(conn => {
        if (conn.ws && conn.ws.readyState === WebSocket.OPEN) {
            conn.ws.close();
        }
    });
    
    // Delete test users
    try {
        const result = await query("DELETE FROM users WHERE email LIKE 'stress-test-user-%'");
        console.log(`   Deleted ${result.rowCount} test users`);
    } catch (error) {
        console.error('❌ Error deleting test users:', error);
    }
    
    console.log('✅ Cleanup completed');
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
    console.log('\n🛑 Test interrupted by user');
    await cleanup();
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('\n🛑 Test terminated');
    await cleanup();
    process.exit(0);
});

// Run the stress test
runStressTest().catch(error => {
    console.error('💥 Unhandled error:', error);
    cleanup().then(() => process.exit(1));
});
