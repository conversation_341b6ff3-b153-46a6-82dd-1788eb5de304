#!/usr/bin/env node

/**
 * Database Performance Monitor
 * 
 * Monitors PostgreSQL performance metrics during stress testing
 * Provides real-time insights into database load and bottlenecks
 */

import { query, getPoolStats } from '../src/config/database.js';
import dotenv from 'dotenv';

dotenv.config();

const MONITOR_INTERVAL = 2000; // 2 seconds
const MONITOR_DURATION = 120000; // 2 minutes (or until stopped)

let isMonitoring = false;
let startTime = null;
let snapshots = [];

/**
 * Get comprehensive database performance metrics
 */
async function getPerformanceMetrics() {
    try {
        // Connection pool stats
        const poolStats = getPoolStats();
        
        // Database connection stats
        const connectionStats = await query(`
            SELECT 
                count(*) as total_connections,
                count(*) FILTER (WHERE state = 'active') as active_connections,
                count(*) FILTER (WHERE state = 'idle') as idle_connections,
                count(*) FILTER (WHERE state = 'idle in transaction') as idle_in_transaction,
                count(*) FILTER (WHERE wait_event_type IS NOT NULL) as waiting_connections
            FROM pg_stat_activity 
            WHERE datname = current_database()
        `);
        
        // Database activity stats
        const activityStats = await query(`
            SELECT 
                sum(numbackends) as backends,
                sum(xact_commit) as commits,
                sum(xact_rollback) as rollbacks,
                sum(blks_read) as blocks_read,
                sum(blks_hit) as blocks_hit,
                sum(tup_returned) as tuples_returned,
                sum(tup_fetched) as tuples_fetched,
                sum(tup_inserted) as tuples_inserted,
                sum(tup_updated) as tuples_updated,
                sum(tup_deleted) as tuples_deleted
            FROM pg_stat_database 
            WHERE datname = current_database()
        `);
        
        // Table-specific stats for our main tables
        const tableStats = await query(`
            SELECT 
                schemaname,
                tablename,
                n_tup_ins as inserts,
                n_tup_upd as updates,
                n_tup_del as deletes,
                n_live_tup as live_tuples,
                n_dead_tup as dead_tuples,
                seq_scan as sequential_scans,
                seq_tup_read as seq_tuples_read,
                idx_scan as index_scans,
                idx_tup_fetch as idx_tuples_fetched
            FROM pg_stat_user_tables 
            WHERE tablename IN ('users', 'api_usage_logs', 'access_tiers')
            ORDER BY tablename
        `);
        
        // Lock stats
        const lockStats = await query(`
            SELECT 
                mode,
                count(*) as lock_count
            FROM pg_locks 
            WHERE database = (SELECT oid FROM pg_database WHERE datname = current_database())
            GROUP BY mode
            ORDER BY lock_count DESC
        `);
        
        // Recent usage logs
        const usageStats = await query(`
            SELECT 
                count(*) as total_logs,
                count(*) FILTER (WHERE created_at > NOW() - INTERVAL '10 seconds') as last_10s,
                count(*) FILTER (WHERE created_at > NOW() - INTERVAL '1 minute') as last_1m,
                COALESCE(sum(credits_consumed) FILTER (WHERE created_at > NOW() - INTERVAL '10 seconds'), 0) as credits_10s,
                COALESCE(avg(credits_consumed) FILTER (WHERE created_at > NOW() - INTERVAL '1 minute'), 0) as avg_credits_1m
            FROM api_usage_logs
        `);
        
        // User credit stats
        const creditStats = await query(`
            SELECT 
                count(*) as total_users,
                count(*) FILTER (WHERE email LIKE 'stress-test-user-%') as test_users,
                avg(credits_remaining) FILTER (WHERE email LIKE 'stress-test-user-%') as avg_test_credits,
                sum(credits_used_this_month) FILTER (WHERE email LIKE 'stress-test-user-%') as total_test_credits_used
            FROM users
        `);
        
        // Database size stats
        const sizeStats = await query(`
            SELECT 
                pg_size_pretty(pg_database_size(current_database())) as database_size,
                pg_size_pretty(pg_total_relation_size('api_usage_logs')) as usage_logs_size,
                pg_size_pretty(pg_total_relation_size('users')) as users_size
        `);
        
        return {
            timestamp: new Date(),
            pool: poolStats,
            connections: connectionStats.rows[0],
            activity: activityStats.rows[0],
            tables: tableStats.rows,
            locks: lockStats.rows,
            usage: usageStats.rows[0],
            credits: creditStats.rows[0],
            sizes: sizeStats.rows[0]
        };
        
    } catch (error) {
        console.error('❌ Error getting performance metrics:', error);
        return null;
    }
}

/**
 * Format and display performance metrics
 */
function displayMetrics(metrics, previousMetrics = null) {
    const elapsed = startTime ? Math.floor((Date.now() - startTime) / 1000) : 0;
    
    console.clear();
    console.log('📊 DATABASE PERFORMANCE MONITOR');
    console.log('================================');
    console.log(`Time: ${metrics.timestamp.toLocaleTimeString()} (${elapsed}s elapsed)`);
    console.log('');
    
    // Connection Pool Status
    console.log('🔗 CONNECTION POOL');
    console.log(`   Pool: ${metrics.pool.totalCount}/${metrics.pool.maxConnections} (${metrics.pool.idleCount} idle)`);
    console.log(`   Health: ${metrics.pool.healthStatus}`);
    
    const poolUtilization = (metrics.pool.totalCount / metrics.pool.maxConnections * 100).toFixed(1);
    if (poolUtilization > 80) {
        console.log(`   ⚠️  Utilization: ${poolUtilization}% (HIGH)`);
    } else if (poolUtilization > 50) {
        console.log(`   ⚡ Utilization: ${poolUtilization}% (MEDIUM)`);
    } else {
        console.log(`   ✅ Utilization: ${poolUtilization}% (LOW)`);
    }
    
    // Database Connections
    console.log('\n🔌 DATABASE CONNECTIONS');
    console.log(`   Total: ${metrics.connections.total_connections}`);
    console.log(`   Active: ${metrics.connections.active_connections}`);
    console.log(`   Idle: ${metrics.connections.idle_connections}`);
    if (parseInt(metrics.connections.waiting_connections) > 0) {
        console.log(`   ⚠️  Waiting: ${metrics.connections.waiting_connections}`);
    }
    if (parseInt(metrics.connections.idle_in_transaction) > 0) {
        console.log(`   ⚠️  Idle in transaction: ${metrics.connections.idle_in_transaction}`);
    }
    
    // Recent Activity
    console.log('\n📈 RECENT ACTIVITY');
    console.log(`   Usage logs (last 10s): ${metrics.usage.last_10s}`);
    console.log(`   Usage logs (last 1m): ${metrics.usage.last_1m}`);
    console.log(`   Credits consumed (10s): ${metrics.usage.credits_10s}`);
    console.log(`   Avg credits/log (1m): ${parseFloat(metrics.usage.avg_credits_1m).toFixed(2)}`);
    
    // Calculate rates if we have previous metrics
    if (previousMetrics) {
        const timeDiff = (metrics.timestamp - previousMetrics.timestamp) / 1000; // seconds
        
        if (timeDiff > 0) {
            const insertRate = (parseInt(metrics.usage.last_10s) / Math.min(10, timeDiff)).toFixed(1);
            console.log(`   Insert rate: ${insertRate} logs/sec`);
            
            // Table operation rates
            const usageTable = metrics.tables.find(t => t.tablename === 'api_usage_logs');
            const prevUsageTable = previousMetrics.tables.find(t => t.tablename === 'api_usage_logs');
            
            if (usageTable && prevUsageTable) {
                const insertDiff = parseInt(usageTable.inserts) - parseInt(prevUsageTable.inserts);
                const updateDiff = parseInt(usageTable.updates) - parseInt(prevUsageTable.updates);
                
                if (insertDiff > 0 || updateDiff > 0) {
                    console.log(`   DB operations: ${insertDiff} inserts, ${updateDiff} updates`);
                }
            }
        }
    }
    
    // Table Statistics
    console.log('\n📋 TABLE STATS');
    metrics.tables.forEach(table => {
        console.log(`   ${table.tablename}:`);
        console.log(`     Live tuples: ${parseInt(table.live_tuples).toLocaleString()}`);
        if (parseInt(table.dead_tuples) > 0) {
            console.log(`     Dead tuples: ${parseInt(table.dead_tuples).toLocaleString()}`);
        }
        console.log(`     Index scans: ${parseInt(table.index_scans).toLocaleString()}`);
        if (parseInt(table.sequential_scans) > 0) {
            console.log(`     Seq scans: ${parseInt(table.sequential_scans).toLocaleString()}`);
        }
    });
    
    // Lock Information
    if (metrics.locks.length > 0) {
        console.log('\n🔒 LOCKS');
        metrics.locks.forEach(lock => {
            if (parseInt(lock.lock_count) > 1) {
                console.log(`   ${lock.mode}: ${lock.lock_count}`);
            }
        });
    }
    
    // Test User Stats
    if (parseInt(metrics.credits.test_users) > 0) {
        console.log('\n👥 TEST USERS');
        console.log(`   Count: ${metrics.credits.test_users}`);
        console.log(`   Avg credits remaining: ${parseInt(metrics.credits.avg_test_credits).toLocaleString()}`);
        console.log(`   Total credits used: ${parseInt(metrics.credits.total_test_credits_used).toLocaleString()}`);
    }
    
    // Database Sizes
    console.log('\n💾 DATABASE SIZES');
    console.log(`   Total database: ${metrics.sizes.database_size}`);
    console.log(`   Usage logs table: ${metrics.sizes.usage_logs_size}`);
    console.log(`   Users table: ${metrics.sizes.users_size}`);
    
    // Performance Warnings
    console.log('\n⚠️  PERFORMANCE ALERTS');
    let hasAlerts = false;
    
    if (poolUtilization > 80) {
        console.log('   🔴 Connection pool utilization is HIGH');
        hasAlerts = true;
    }
    
    if (parseInt(metrics.connections.waiting_connections) > 0) {
        console.log('   🔴 Connections are waiting for resources');
        hasAlerts = true;
    }
    
    if (parseInt(metrics.usage.last_10s) > 100) {
        console.log('   🟡 High database write activity');
        hasAlerts = true;
    }
    
    const usageTable = metrics.tables.find(t => t.tablename === 'api_usage_logs');
    if (usageTable && parseInt(usageTable.dead_tuples) > parseInt(usageTable.live_tuples) * 0.1) {
        console.log('   🟡 High dead tuple ratio - consider VACUUM');
        hasAlerts = true;
    }
    
    if (!hasAlerts) {
        console.log('   ✅ No performance issues detected');
    }
    
    console.log('\n📝 Press Ctrl+C to stop monitoring');
}

/**
 * Start monitoring
 */
async function startMonitoring() {
    console.log('🚀 Starting database performance monitoring...');
    console.log(`📊 Monitoring interval: ${MONITOR_INTERVAL / 1000}s`);
    console.log('');
    
    isMonitoring = true;
    startTime = Date.now();
    let previousMetrics = null;
    
    const monitorInterval = setInterval(async () => {
        if (!isMonitoring) {
            clearInterval(monitorInterval);
            return;
        }
        
        const metrics = await getPerformanceMetrics();
        if (metrics) {
            snapshots.push(metrics);
            displayMetrics(metrics, previousMetrics);
            previousMetrics = metrics;
        }
    }, MONITOR_INTERVAL);
    
    // Auto-stop after duration (optional)
    if (MONITOR_DURATION > 0) {
        setTimeout(() => {
            stopMonitoring();
        }, MONITOR_DURATION);
    }
}

/**
 * Stop monitoring
 */
async function stopMonitoring() {
    isMonitoring = false;
    console.log('\n🛑 Monitoring stopped');

    if (snapshots.length > 0) {
        console.log(`📊 Collected ${snapshots.length} performance snapshots`);

        // Save snapshots to file for analysis
        const fs = await import('fs');
        const filename = `db-performance-${Date.now()}.json`;
        fs.writeFileSync(filename, JSON.stringify(snapshots, null, 2));
        console.log(`💾 Performance data saved to ${filename}`);
    }

    process.exit(0);
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
    console.log('\n🛑 Monitoring interrupted by user');
    await stopMonitoring();
});

process.on('SIGTERM', async () => {
    console.log('\n🛑 Monitoring terminated');
    await stopMonitoring();
});

// Start monitoring
startMonitoring().catch(error => {
    console.error('💥 Monitoring failed:', error);
    process.exit(1);
});
