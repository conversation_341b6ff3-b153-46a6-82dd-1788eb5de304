#!/usr/bin/env node

/**
 * Stress Test Runner
 * 
 * Orchestrates the KOL feed stress test with database monitoring
 * Provides a comprehensive view of system performance under load
 */

import { spawn } from 'child_process';
import { testConnection } from '../src/config/database.js';
import axios from 'axios';
import dotenv from 'dotenv';

dotenv.config();

const API_BASE_URL = 'http://localhost:3001';

/**
 * Check if the API server is running
 */
async function checkServerStatus() {
    try {
        const response = await axios.get(`${API_BASE_URL}/health`, { timeout: 5000 });
        return response.status === 200;
    } catch (error) {
        return false;
    }
}

/**
 * Check if KOL feed stream is active
 */
async function checkKolFeedStatus() {
    try {
        const response = await axios.get(`${API_BASE_URL}/ws-api/info`, { timeout: 5000 });
        const streams = response.data.available_streams || [];
        return streams.includes('kol-feed');
    } catch (error) {
        console.warn('⚠️  Could not verify KOL feed status:', error.message);
        return true; // Assume it's available
    }
}

/**
 * Run a child process and return a promise
 */
function runProcess(command, args, options = {}) {
    return new Promise((resolve, reject) => {
        const child = spawn(command, args, {
            stdio: 'inherit',
            ...options
        });
        
        child.on('close', (code) => {
            if (code === 0) {
                resolve(code);
            } else {
                reject(new Error(`Process exited with code ${code}`));
            }
        });
        
        child.on('error', (error) => {
            reject(error);
        });
        
        return child;
    });
}

/**
 * Main stress test execution
 */
async function runStressTest() {
    console.log('🔥 KOL Feed Stress Test Runner');
    console.log('===============================');
    console.log('');
    
    // Pre-flight checks
    console.log('🔍 Running pre-flight checks...');
    
    // Check database connection
    console.log('   Checking database connection...');
    const dbConnected = await testConnection();
    if (!dbConnected) {
        console.error('❌ Database connection failed');
        console.error('   Please ensure PostgreSQL is running and configured correctly');
        process.exit(1);
    }
    console.log('   ✅ Database connection OK');
    
    // Check API server
    console.log('   Checking API server...');
    const serverRunning = await checkServerStatus();
    if (!serverRunning) {
        console.error('❌ API server is not running');
        console.error('   Please start the server with: pnpm dev');
        process.exit(1);
    }
    console.log('   ✅ API server is running');
    
    // Check KOL feed
    console.log('   Checking KOL feed stream...');
    const kolFeedActive = await checkKolFeedStatus();
    if (!kolFeedActive) {
        console.warn('⚠️  KOL feed stream may not be active');
        console.warn('   The test will still run, but may not receive messages');
    } else {
        console.log('   ✅ KOL feed stream is available');
    }
    
    console.log('');
    console.log('✅ All pre-flight checks passed');
    console.log('');
    
    // Ask user for confirmation
    console.log('⚠️  WARNING: This test will:');
    console.log('   • Create 100 test user accounts');
    console.log('   • Open 100 WebSocket connections');
    console.log('   • Generate high database load');
    console.log('   • Run for 60 seconds');
    console.log('');
    
    // In a real interactive environment, you might want to prompt for confirmation
    // For now, we'll proceed automatically
    console.log('🚀 Starting stress test in 3 seconds...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('');
    console.log('📊 Starting database monitoring...');
    console.log('📱 Starting stress test...');
    console.log('');
    console.log('💡 TIP: Open another terminal to run additional monitoring:');
    console.log('   node tests/monitor_database_performance.js');
    console.log('');
    
    try {
        // Start the stress test
        await runProcess('node', ['tests/stress_test_kol_feed.js']);
        
        console.log('');
        console.log('🎉 Stress test completed successfully!');
        
    } catch (error) {
        console.error('');
        console.error('❌ Stress test failed:', error.message);
        process.exit(1);
    }
}

/**
 * Display usage information
 */
function showUsage() {
    console.log('🔥 KOL Feed Stress Test Runner');
    console.log('===============================');
    console.log('');
    console.log('This script runs a comprehensive stress test of the KOL feed stream');
    console.log('with 100 concurrent users to test database performance.');
    console.log('');
    console.log('Usage:');
    console.log('  node tests/run_stress_test.js');
    console.log('');
    console.log('Prerequisites:');
    console.log('  • PostgreSQL database running');
    console.log('  • API server running (pnpm dev)');
    console.log('  • KOL feed stream configured');
    console.log('');
    console.log('What the test does:');
    console.log('  • Creates 100 test users with Basic tier (1M credits each)');
    console.log('  • Opens 100 WebSocket connections');
    console.log('  • Subscribes all users to kol-feed stream');
    console.log('  • Monitors for 60 seconds');
    console.log('  • Reports performance metrics');
    console.log('  • Cleans up test data');
    console.log('');
    console.log('Monitoring:');
    console.log('  • Database connection pool usage');
    console.log('  • Credit consumption rates');
    console.log('  • Message throughput');
    console.log('  • Error rates');
    console.log('  • Performance bottlenecks');
    console.log('');
    console.log('Options:');
    console.log('  --help, -h    Show this help message');
    console.log('  --monitor     Run database monitoring only');
    console.log('');
}

/**
 * Run database monitoring only
 */
async function runMonitoringOnly() {
    console.log('📊 Starting database monitoring...');
    console.log('Press Ctrl+C to stop');
    console.log('');
    
    try {
        await runProcess('node', ['tests/monitor_database_performance.js']);
    } catch (error) {
        console.error('❌ Monitoring failed:', error.message);
        process.exit(1);
    }
}

// Parse command line arguments
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
    showUsage();
    process.exit(0);
}

if (args.includes('--monitor')) {
    runMonitoringOnly().catch(error => {
        console.error('💥 Unhandled error:', error);
        process.exit(1);
    });
} else {
    runStressTest().catch(error => {
        console.error('💥 Unhandled error:', error);
        process.exit(1);
    });
}

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Test runner interrupted by user');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Test runner terminated');
    process.exit(0);
});
