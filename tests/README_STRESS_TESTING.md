# KOL Feed Stress Testing Guide

This guide explains how to stress test the KOL feed stream with 100 concurrent users to evaluate database performance and identify potential bottlenecks.

## Overview

The stress test simulates a high-load scenario where 100 users simultaneously connect to the KOL feed stream. This helps identify:

- Database connection pool limitations
- Credit system performance under load
- WebSocket handling capacity
- Memory usage patterns
- Potential bottlenecks

## Test Components

### 1. Stress Test (`stress_test_kol_feed.js`)
- Creates 100 test users with Basic tier (1M credits each)
- Opens 100 WebSocket connections
- Subscribes all users to `kol-feed` stream
- Monitors for 60 seconds
- Tracks messages, credits, and errors
- Cleans up test data automatically

### 2. Database Monitor (`monitor_database_performance.js`)
- Real-time database performance monitoring
- Connection pool utilization
- Query performance metrics
- Table statistics
- Lock information
- Performance alerts

### 3. Test Runner (`run_stress_test.js`)
- Orchestrates the complete test process
- Pre-flight checks (database, server, KOL feed)
- Runs stress test with monitoring
- Provides comprehensive reporting

## Quick Start

### Prerequisites

1. **Start the API server:**
   ```bash
   pnpm dev
   ```

2. **Ensure KOL feed is active:**
   - Check that the KOL feed worker is running
   - Verify external KOL data source is connected

### Run Complete Stress Test

```bash
# Run the full stress test with monitoring
pnpm test:stress
```

This will:
1. Check all prerequisites
2. Create 100 test users
3. Run the stress test
4. Monitor database performance
5. Generate a comprehensive report
6. Clean up test data

### Run Individual Components

```bash
# Run only the stress test
pnpm test:stress:kol-feed

# Run only database monitoring
pnpm test:monitor-db
```

## Understanding the Results

### Connection Metrics
- **Successful Connections**: Should be close to 100
- **Failed Connections**: Should be 0 or very low
- **Connection Rate**: How quickly connections are established

### Message Metrics
- **Messages Received**: Total stream messages received by all users
- **Messages/Second**: Throughput rate
- **Credits Consumed**: Total credits deducted (2 per message per user)

### Database Performance
- **Pool Utilization**: Percentage of connection pool used
- **Active Connections**: Current active database connections
- **Query Performance**: Response times and throughput

### Performance Assessment

#### ✅ Excellent Performance
- Pool utilization < 50%
- No connection failures
- No waiting connections
- Error rate < 1%

#### ⚡ Good Performance
- Pool utilization 50-80%
- Minimal connection failures
- Low error rate < 5%

#### ⚠️ Performance Issues
- Pool utilization > 80%
- Connection failures > 10%
- High error rate > 10%
- Connections waiting for resources

#### ❌ Critical Issues
- Pool exhaustion (100% utilization)
- High connection failure rate
- Database timeouts
- System instability

## Expected Results

### Baseline Performance (No Optimizations)
With the current database setup, you might see:
- **Pool Utilization**: 60-80%
- **Database Writes**: ~200 operations/second
- **Credit Checks**: ~100 queries/second
- **Potential Issues**: Connection pool pressure

### Performance Bottlenecks to Watch For

1. **Connection Pool Exhaustion**
   - Symptom: Pool utilization > 90%
   - Impact: New connections fail or timeout
   - Solution: Increase `DB_MAX_CONNECTIONS`

2. **High Database Write Load**
   - Symptom: Many INSERT operations to `api_usage_logs`
   - Impact: Slower response times
   - Solution: Implement batch processing

3. **Credit System Bottleneck**
   - Symptom: Many UPDATE operations on `users` table
   - Impact: Lock contention, slow credit checks
   - Solution: Redis-based credit tracking

4. **Memory Issues**
   - Symptom: High memory usage, slow performance
   - Impact: System instability
   - Solution: Optimize data structures, add monitoring

## Troubleshooting

### Test Fails to Start
```bash
# Check database connection
psql -d your_database -c "SELECT 1;"

# Check API server
curl http://localhost:3001/health

# Check KOL feed status
curl http://localhost:3001/ws-api/info
```

### Low Connection Success Rate
- Check WebSocket connection limits in user tiers
- Verify API keys are valid
- Check server resource availability

### No Messages Received
- Verify KOL feed worker is running
- Check external data source connectivity
- Ensure stream is properly configured

### High Error Rate
- Check application logs for specific errors
- Monitor database error logs
- Verify credit balances

## Monitoring During Test

### Real-time Monitoring
Open multiple terminals to monitor different aspects:

```bash
# Terminal 1: Run the stress test
pnpm test:stress

# Terminal 2: Monitor database performance
pnpm test:monitor-db

# Terminal 3: Watch application logs
tail -f logs/app.log

# Terminal 4: Monitor system resources
htop
```

### Database Queries
Monitor database activity during the test:

```sql
-- Active connections
SELECT count(*) FROM pg_stat_activity WHERE state = 'active';

-- Recent usage logs
SELECT count(*) FROM api_usage_logs WHERE created_at > NOW() - INTERVAL '1 minute';

-- Lock information
SELECT mode, count(*) FROM pg_locks GROUP BY mode;

-- Table sizes
SELECT pg_size_pretty(pg_total_relation_size('api_usage_logs'));
```

## Performance Optimization

Based on test results, consider these optimizations:

### 1. Database Connection Pool
```bash
# In .env file
DB_MAX_CONNECTIONS=50  # Increase from default 20
DB_MIN_CONNECTIONS=5
```

### 2. Batch Processing
Implement batch credit operations to reduce database load:
- Group credit operations every 5 seconds
- Bulk insert usage logs
- Reduce individual database transactions

### 3. Redis Caching
Use Redis for high-frequency operations:
- Cache user credit balances
- Batch credit updates
- Reduce database queries

### 4. Database Indexing
Ensure proper indexes for common queries:
```sql
CREATE INDEX CONCURRENTLY idx_api_usage_logs_user_created 
ON api_usage_logs (user_id, created_at DESC);
```

## Test Data Cleanup

The stress test automatically cleans up test data, but you can manually verify:

```sql
-- Check for remaining test users
SELECT count(*) FROM users WHERE email LIKE 'stress-test-user-%';

-- Delete test users if needed
DELETE FROM users WHERE email LIKE 'stress-test-user-%';

-- Check usage logs from test
SELECT count(*) FROM api_usage_logs 
WHERE user_id IN (
    SELECT id FROM users WHERE email LIKE 'stress-test-user-%'
);
```

## Continuous Testing

Consider running stress tests regularly:

1. **Before deployments** - Ensure changes don't degrade performance
2. **After optimizations** - Verify improvements
3. **Capacity planning** - Understand system limits
4. **Load testing** - Simulate real-world usage patterns

## Integration with CI/CD

Example GitHub Actions workflow:

```yaml
name: Stress Test
on:
  push:
    branches: [main]
  
jobs:
  stress-test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: pnpm install
      
      - name: Setup database
        run: |
          psql -h localhost -U postgres -d postgres -f SQL/01_create_tables.sql
          psql -h localhost -U postgres -d postgres -f SQL/02_create_indexes.sql
          # ... other migrations
      
      - name: Start API server
        run: pnpm dev &
        
      - name: Wait for server
        run: sleep 10
      
      - name: Run stress test
        run: pnpm test:stress
```

This comprehensive stress testing setup will help you identify and resolve performance bottlenecks before they impact production users.
