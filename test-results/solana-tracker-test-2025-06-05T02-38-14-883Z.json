{"summary": {"total": 10, "successful": 2, "failed": 0, "timedOut": 8, "startTime": "2025-06-05T02:09:11.129Z", "endTime": "2025-06-05T02:38:14.882Z", "totalDuration": 1743753}, "streams": {"tokens-launched": {"status": "timeout", "subscriptionConfirmed": false, "messageReceived": false, "duration": 60007, "error": "No messages received within 1m timeout", "connectionId": "b8871b26-cb70-4b88-83fb-9b98ff2f1ad9"}, "pool-changes": {"status": "timeout", "subscriptionConfirmed": true, "messageReceived": false, "duration": 60003, "error": "No messages received within 1m timeout", "connectionId": "7f879387-18dd-40de-a97b-dcd8a2f29498"}, "price-updates": {"status": "timeout", "subscriptionConfirmed": true, "messageReceived": false, "duration": 60001, "error": "No messages received within 1m timeout", "connectionId": "5b42de6b-3961-4e61-9ccd-ceb57d9a58d0"}, "tokens-graduating": {"status": "success", "subscriptionConfirmed": true, "messageReceived": true, "duration": 2904, "sampleData": {"type": "graduating", "timestamp": *************, "token": {"name": "AINTERN", "symbol": "INTERN", "mint": "FAn23BZmp1oSC8eaFaUqnTW2Z4ENz5mdpeuJ1uCpump", "uri": "https://ipfs.io/ipfs/QmRYH71TPc5PtB6LMLEYWaJqfD5S6h3JMX4kvVhXfPyF8N", "decimals": 6, "hasFileMetaData": true, "createdOn": "https://pump.fun", "description": "Your AI social media intern that learns from any Twitter account. Choose the voice you want, and your AI posts in that style. Automate your social game.", "image": "https://image.solanatracker.io/proxy?url=https%3A%2F%2Fipfs.io%2Fipfs%2FQmXj7Q7Gq3uAxrQbkzLQm2SgVs1oPZeeDTyM6KfzKy6p5U", "showName": true, "twitter": "https://x.com/aintern_fun", "telegram": "https://t.me/aintern_fun", "website": "https://www.aintern.fun/"}}, "connectionId": "2182e080-fc5f-45c1-a28c-280411816a45"}, "token-transactions": {"status": "timeout", "subscriptionConfirmed": true, "messageReceived": false, "duration": 180004, "error": "No messages received within 3m timeout", "connectionId": "8df28f14-59e7-4f11-a5de-8e97e9ae1a48"}, "tokens-graduated": {"status": "success", "subscriptionConfirmed": true, "messageReceived": true, "duration": 160794, "sampleData": {"type": "graduated", "timestamp": *************, "token": {"name": "dolf", "symbol": "dolf", "mint": "BrEqjLgUfxAsKJCqhRXxfzGg7DbTEGzQwrQYS97cpump", "uri": "https://ipfs.io/ipfs/bafkreibpd4yiidhbdu26sppvw2ibuh6433wlrevaik4b24q65isz5foczq", "decimals": 6, "hasFileMetaData": true, "createdOn": "https://pump.fun", "description": "", "image": "https://ipfs.io/ipfs/bafybeic5usorzqua4njkj3hcs5fyfgng7xc7oeoprnzt3klbkpo2zshjvq", "showName": true, "twitter": "https://x.com/i/communities/1930442830352109649", "website": "https://x.com/i/communities/1930442830352109649"}}, "connectionId": "dd843d1d-39a2-4222-8c13-70fa9d605f86"}, "wallet-transactions": {"status": "timeout", "subscriptionConfirmed": true, "messageReceived": false, "duration": 300004, "error": "No messages received within 5m timeout", "connectionId": "b249e750-0b81-4420-9f71-5c97d4cb7cfe"}, "token-metadata": {"status": "timeout", "subscriptionConfirmed": true, "messageReceived": false, "duration": 300005, "error": "No messages received within 5m timeout", "connectionId": "2fd89da7-1df4-4359-af1b-40b3c3f8bacb"}, "token-holders": {"status": "timeout", "subscriptionConfirmed": true, "messageReceived": false, "duration": 300005, "error": "No messages received within 5m timeout", "connectionId": "3b71ad2a-2357-42ee-a1ed-1f9e33267617"}, "token-changes": {"status": "timeout", "subscriptionConfirmed": true, "messageReceived": false, "duration": 300010, "error": "No messages received within 5m timeout", "connectionId": "8f5a2073-b1d1-4e1a-ab86-60432f9bcdbc"}}, "errors": [], "recommendations": ["⏰ Some streams timed out. This may be normal for low-frequency streams or indicate SolanaTracker connectivity issues."]}