#!/usr/bin/env node

/**
 * Initialize Redis Credit System
 * 
 * This script initializes the Redis-based credit tracking system by:
 * 1. Testing Redis connectivity
 * 2. Loading existing user credits from PostgreSQL to Redis
 * 3. Starting the credit tracker
 * 4. Verifying the system is working correctly
 */

import { redisCreditTracker } from '../services/RedisCreditTracker.js';
import { redis } from '../config/redis.js';
import { query, testConnection } from '../config/database.js';
import { getRedisConfig, enableAllOptimizations, logConfiguration } from '../config/performance.js';

/**
 * Test Redis connectivity
 */
async function testRedisConnection() {
    try {
        console.log('🔍 Testing Redis connection...');
        const pong = await redis.ping();
        if (pong === 'PONG') {
            console.log('✅ Redis connection successful');
            return true;
        } else {
            console.error('❌ Redis ping returned unexpected response:', pong);
            return false;
        }
    } catch (error) {
        console.error('❌ Redis connection failed:', error.message);
        return false;
    }
}

/**
 * Test PostgreSQL connectivity
 */
async function testDatabaseConnection() {
    try {
        console.log('🔍 Testing PostgreSQL connection...');
        const connected = await testConnection();
        if (connected) {
            console.log('✅ PostgreSQL connection successful');
            return true;
        } else {
            console.error('❌ PostgreSQL connection failed');
            return false;
        }
    } catch (error) {
        console.error('❌ PostgreSQL connection error:', error.message);
        return false;
    }
}

/**
 * Get user statistics from database
 */
async function getUserStats() {
    try {
        const result = await query(`
            SELECT 
                COUNT(*) as total_users,
                COUNT(*) FILTER (WHERE is_active = true) as active_users,
                SUM(credits_remaining) FILTER (WHERE is_active = true) as total_credits,
                AVG(credits_remaining) FILTER (WHERE is_active = true) as avg_credits
            FROM users
        `);
        
        return result.rows[0];
    } catch (error) {
        console.error('❌ Error getting user stats:', error);
        return null;
    }
}

/**
 * Test credit operations
 */
async function testCreditOperations() {
    try {
        console.log('🧪 Testing credit operations...');
        
        // Get a test user
        const userResult = await query(`
            SELECT id, credits_remaining 
            FROM users 
            WHERE is_active = true 
            AND credits_remaining > 10 
            LIMIT 1
        `);
        
        if (userResult.rows.length === 0) {
            console.log('⚠️ No suitable test user found (need active user with >10 credits)');
            return false;
        }
        
        const testUser = userResult.rows[0];
        const userId = testUser.id;
        const initialCredits = testUser.credits_remaining;
        
        console.log(`   Testing with user ${userId} (${initialCredits} credits)`);
        
        // Test credit check
        const hasCredits = await redisCreditTracker.checkCredits(userId, 5);
        console.log(`   Credit check (5 credits): ${hasCredits ? '✅' : '❌'}`);
        
        if (!hasCredits) {
            console.log('⚠️ User does not have sufficient credits for test');
            return false;
        }
        
        // Test credit consumption
        const consumed = await redisCreditTracker.consumeCredits(userId, 2, {
            test: true,
            endpoint: 'test://init-script'
        });
        console.log(`   Credit consumption (2 credits): ${consumed ? '✅' : '❌'}`);
        
        // Test batch check
        const batchResult = await redisCreditTracker.batchCheckCredits([userId], 3);
        console.log(`   Batch credit check: ${batchResult.has(userId) ? '✅' : '❌'}`);
        
        return consumed;
        
    } catch (error) {
        console.error('❌ Error testing credit operations:', error);
        return false;
    }
}

/**
 * Clean up test data
 */
async function cleanupTestData() {
    try {
        console.log('🧹 Cleaning up test data...');
        
        // Remove test sync queue items
        const queueLength = await redis.llen('credit_sync_queue');
        if (queueLength > 0) {
            const items = await redis.lrange('credit_sync_queue', 0, -1);
            const testItems = items.filter(item => {
                try {
                    const data = JSON.parse(item);
                    return data.metadata && data.metadata.test === true;
                } catch {
                    return false;
                }
            });
            
            if (testItems.length > 0) {
                // Remove test items from queue
                for (const testItem of testItems) {
                    await redis.lrem('credit_sync_queue', 1, testItem);
                }
                console.log(`   Removed ${testItems.length} test items from sync queue`);
            }
        }
        
        console.log('✅ Cleanup completed');
        
    } catch (error) {
        console.error('❌ Error during cleanup:', error);
    }
}

/**
 * Main initialization function
 */
async function initializeRedisCredits() {
    console.log('🚀 Initializing Redis Credit System');
    console.log('====================================');
    
    try {
        // Show current configuration
        console.log('\n📋 Current Configuration:');
        logConfiguration();
        
        const config = getRedisConfig();
        if (!config.enabled) {
            console.log('\n⚠️ Redis credits are disabled in current configuration');
            console.log('   To enable, set NODE_ENV=production or modify performance config');
            
            // Ask if user wants to enable for this session
            console.log('\n🔧 Enabling Redis credits for this initialization...');
            enableAllOptimizations();
            logConfiguration();
        }
        
        // Test connections
        console.log('\n🔍 Testing Connections:');
        const redisOk = await testRedisConnection();
        const dbOk = await testDatabaseConnection();
        
        if (!redisOk || !dbOk) {
            console.error('\n❌ Connection tests failed. Please check your Redis and PostgreSQL configuration.');
            process.exit(1);
        }
        
        // Get user statistics
        console.log('\n📊 User Statistics:');
        const userStats = await getUserStats();
        if (userStats) {
            console.log(`   Total users: ${userStats.total_users}`);
            console.log(`   Active users: ${userStats.active_users}`);
            console.log(`   Total credits: ${parseInt(userStats.total_credits || 0).toLocaleString()}`);
            console.log(`   Average credits: ${parseFloat(userStats.avg_credits || 0).toFixed(2)}`);
        }
        
        // Initialize Redis credit tracker
        console.log('\n🔄 Initializing Redis Credit Tracker:');
        await redisCreditTracker.init();
        
        // Test credit operations
        console.log('\n🧪 Testing Credit Operations:');
        const testPassed = await testCreditOperations();
        
        if (testPassed) {
            console.log('✅ All tests passed!');
        } else {
            console.log('⚠️ Some tests failed, but system may still be functional');
        }
        
        // Show tracker statistics
        console.log('\n📈 Tracker Statistics:');
        const stats = redisCreditTracker.getStats();
        console.log(`   Initialized: ${stats.isInitialized ? '✅' : '❌'}`);
        console.log(`   Running: ${stats.isRunning ? '✅' : '❌'}`);
        console.log(`   Enabled: ${stats.enabled ? '✅' : '❌'}`);
        console.log(`   Total operations: ${stats.totalOperations}`);
        console.log(`   Fallback operations: ${stats.fallbackOperations}`);
        
        // Clean up test data
        await cleanupTestData();
        
        console.log('\n🎉 Redis Credit System initialization completed!');
        console.log('\n💡 Next Steps:');
        console.log('   • The system is now ready for production use');
        console.log('   • Credit operations will use Redis for high performance');
        console.log('   • Data is automatically synced to PostgreSQL every 30 seconds');
        console.log('   • Monitor the logs for sync operations and performance metrics');
        
        // Keep the process running for a moment to show sync activity
        console.log('\n⏱️ Monitoring sync activity for 10 seconds...');
        setTimeout(async () => {
            const finalStats = redisCreditTracker.getStats();
            console.log('📊 Final Statistics:');
            console.log(`   Total syncs: ${finalStats.totalSyncs}`);
            console.log(`   Last sync: ${finalStats.lastSyncTime || 'None'}`);
            console.log(`   Sync errors: ${finalStats.syncErrors}`);
            
            console.log('\n✅ Initialization complete. System is ready!');
            process.exit(0);
        }, 10000);
        
    } catch (error) {
        console.error('\n💥 Initialization failed:', error);
        process.exit(1);
    }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
    console.log('\n🛑 Initialization interrupted by user');
    await cleanupTestData();
    await redisCreditTracker.stop();
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('\n🛑 Initialization terminated');
    await cleanupTestData();
    await redisCreditTracker.stop();
    process.exit(0);
});

// Run initialization
initializeRedisCredits().catch(error => {
    console.error('💥 Unhandled error:', error);
    process.exit(1);
});
