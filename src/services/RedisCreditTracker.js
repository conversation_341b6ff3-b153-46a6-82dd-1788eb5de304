import { redis, cache } from '../config/redis.js';
import { query } from '../config/database.js';
import { getRedisConfig } from '../config/performance.js';

/**
 * Redis-Based Credit Tracker
 * 
 * Provides high-performance credit tracking using Redis for real-time operations
 * with periodic synchronization to PostgreSQL. This approach:
 * 1. Eliminates database bottlenecks for credit checks
 * 2. Provides atomic credit operations using Redis
 * 3. Maintains data consistency with periodic sync
 * 4. Handles failover scenarios gracefully
 */
export class RedisCreditTracker {
    constructor(options = {}) {
        // Get configuration from performance config
        const config = getRedisConfig();
        
        // Configuration with fallbacks
        this.syncInterval = options.syncInterval || config.syncInterval || 30000;
        this.keyPrefix = options.keyPrefix || config.keyPrefix || 'credits:';
        this.syncQueueKey = options.syncQueueKey || config.syncQueueKey || 'credit_sync_queue';
        this.maxSyncBatchSize = options.maxSyncBatchSize || config.syncBatchSize || 500;
        this.creditTTL = options.creditTTL || config.creditTTL || 86400;
        this.maxRetries = options.maxRetries || config.maxRetries || 3;
        this.fallbackToDatabase = options.fallbackToDatabase !== undefined ? options.fallbackToDatabase : config.fallbackToDatabase;
        this.enabled = options.enabled !== undefined ? options.enabled : config.enabled;
        
        // Internal state
        this.syncIntervalId = null;
        this.isInitialized = false;
        this.stats = {
            totalOperations: 0,
            totalSyncs: 0,
            syncErrors: 0,
            fallbackOperations: 0,
            lastSyncTime: null,
            lastSyncSize: 0
        };

        console.log('🔄 RedisCreditTracker initialized with', {
            enabled: this.enabled,
            syncInterval: this.syncInterval,
            maxSyncBatchSize: this.maxSyncBatchSize,
            fallbackToDatabase: this.fallbackToDatabase
        });
    }

    /**
     * Initialize the credit tracker
     */
    async init() {
        if (!this.enabled) {
            console.log('⚠️ RedisCreditTracker disabled - using database fallback');
            return;
        }

        if (this.isInitialized) {
            console.warn('⚠️ RedisCreditTracker already initialized');
            return;
        }

        try {
            // Test Redis connection
            await redis.ping();
            
            // Load user credit balances from database to Redis
            await this.loadUserCreditsToRedis();
            
            // Start periodic sync
            this.startPeriodicSync();
            
            this.isInitialized = true;
            console.log('✅ RedisCreditTracker initialized successfully');
            
        } catch (error) {
            console.error('❌ Failed to initialize RedisCreditTracker:', error);
            if (this.fallbackToDatabase) {
                console.log('🔄 Falling back to database-only mode');
                this.enabled = false;
            } else {
                throw error;
            }
        }
    }

    /**
     * Load user credit balances from PostgreSQL to Redis
     */
    async loadUserCreditsToRedis() {
        try {
            console.log('🔄 Loading user credits from database to Redis...');
            
            const result = await query(`
                SELECT id, credits_remaining, credits_used_this_month, tier_id
                FROM users 
                WHERE is_active = true
            `);

            const pipeline = redis.pipeline();
            let loadedCount = 0;

            for (const user of result.rows) {
                const creditKey = `${this.keyPrefix}${user.id}`;
                const creditData = {
                    remaining: user.credits_remaining,
                    used_this_month: user.credits_used_this_month,
                    tier_id: user.tier_id,
                    last_updated: Date.now()
                };
                
                pipeline.hset(creditKey, creditData);
                pipeline.expire(creditKey, this.creditTTL);
                loadedCount++;
            }

            await pipeline.exec();
            console.log(`✅ Loaded ${loadedCount} user credit balances to Redis`);
            
        } catch (error) {
            console.error('❌ Failed to load user credits to Redis:', error);
            throw error;
        }
    }

    /**
     * Check if user has sufficient credits for an operation
     * @param {string} userId - User ID
     * @param {number} requiredCredits - Credits required
     * @returns {Promise<boolean>} - True if user has sufficient credits
     */
    async checkCredits(userId, requiredCredits) {
        if (!this.enabled) {
            return await this.checkCreditsFromDB(userId, requiredCredits);
        }

        try {
            const creditKey = `${this.keyPrefix}${userId}`;
            const remaining = await redis.hget(creditKey, 'remaining');
            
            if (remaining === null) {
                // User not in Redis, load from database
                const loaded = await this.loadUserCreditsFromDB(userId);
                if (!loaded) {
                    return false;
                }
                const newRemaining = await redis.hget(creditKey, 'remaining');
                return newRemaining !== null && parseInt(newRemaining) >= requiredCredits;
            }
            
            return parseInt(remaining) >= requiredCredits;
            
        } catch (error) {
            console.error('❌ Error checking credits in Redis:', error);
            this.stats.fallbackOperations++;
            
            if (this.fallbackToDatabase) {
                return await this.checkCreditsFromDB(userId, requiredCredits);
            }
            return false;
        }
    }

    /**
     * Consume credits atomically using Redis
     * @param {string} userId - User ID
     * @param {number} creditsToConsume - Credits to consume
     * @param {Object} metadata - Additional metadata for sync
     * @returns {Promise<boolean>} - True if credits were consumed successfully
     */
    async consumeCredits(userId, creditsToConsume, metadata = {}) {
        if (creditsToConsume === 0) {
            return true; // No credits to consume
        }

        if (!this.enabled) {
            return await this.consumeCreditsFromDB(userId, creditsToConsume, metadata);
        }

        try {
            const creditKey = `${this.keyPrefix}${userId}`;
            
            // Use Lua script for atomic operation
            const luaScript = `
                local creditKey = KEYS[1]
                local syncQueueKey = KEYS[2]
                local creditsToConsume = tonumber(ARGV[1])
                local syncData = ARGV[2]
                
                -- Get current credits
                local remaining = redis.call('HGET', creditKey, 'remaining')
                if not remaining then
                    return {0, 'USER_NOT_FOUND'}
                end
                
                remaining = tonumber(remaining)
                
                -- Check if sufficient credits
                if remaining < creditsToConsume then
                    return {0, 'INSUFFICIENT_CREDITS'}
                end
                
                -- Consume credits
                local newRemaining = remaining - creditsToConsume
                local usedThisMonth = redis.call('HGET', creditKey, 'used_this_month') or 0
                local newUsedThisMonth = tonumber(usedThisMonth) + creditsToConsume
                
                -- Update Redis
                redis.call('HSET', creditKey, 'remaining', newRemaining)
                redis.call('HSET', creditKey, 'used_this_month', newUsedThisMonth)
                redis.call('HSET', creditKey, 'last_updated', ARGV[3])
                
                -- Queue for database sync
                redis.call('LPUSH', syncQueueKey, syncData)
                
                return {1, newRemaining}
            `;

            const syncData = JSON.stringify({
                userId,
                creditsConsumed: creditsToConsume,
                timestamp: Date.now(),
                metadata
            });

            const result = await redis.eval(
                luaScript,
                2, // Number of keys
                creditKey,
                this.syncQueueKey,
                creditsToConsume,
                syncData,
                Date.now()
            );

            const [success, value] = result;
            
            if (success === 1) {
                this.stats.totalOperations++;
                console.log(`💳 Consumed ${creditsToConsume} credits for user ${userId}, remaining: ${value}`);
                return true;
            } else {
                console.warn(`⚠️ Failed to consume credits for user ${userId}: ${value}`);
                return false;
            }
            
        } catch (error) {
            console.error('❌ Error consuming credits in Redis:', error);
            this.stats.fallbackOperations++;
            
            if (this.fallbackToDatabase) {
                return await this.consumeCreditsFromDB(userId, creditsToConsume, metadata);
            }
            return false;
        }
    }

    /**
     * Batch check credits for multiple users
     * @param {string[]} userIds - Array of user IDs
     * @param {number} requiredCredits - Credits required per user
     * @returns {Promise<Set<string>>} - Set of user IDs with sufficient credits
     */
    async batchCheckCredits(userIds, requiredCredits) {
        const eligibleUsers = new Set();
        
        if (!this.enabled) {
            return await this.batchCheckCreditsFromDB(userIds, requiredCredits);
        }
        
        try {
            const pipeline = redis.pipeline();
            
            // Queue all credit checks
            for (const userId of userIds) {
                const creditKey = `${this.keyPrefix}${userId}`;
                pipeline.hget(creditKey, 'remaining');
            }
            
            const results = await pipeline.exec();

            // Track users not found in Redis for database fallback
            const missingUsers = [];

            // Process results
            for (let i = 0; i < userIds.length; i++) {
                const userId = userIds[i];
                const [error, remaining] = results[i];

                if (!error && remaining !== null && parseInt(remaining) >= requiredCredits) {
                    eligibleUsers.add(userId);
                } else if (!error && remaining === null) {
                    // User not found in Redis, need to check database
                    missingUsers.push(userId);
                }
            }

            // Handle missing users by checking database
            if (missingUsers.length > 0) {
                console.log(`🔍 ${missingUsers.length} users not found in Redis, checking database...`);
                const dbEligible = await this.batchCheckCreditsFromDB(missingUsers, requiredCredits);
                dbEligible.forEach(userId => eligibleUsers.add(userId));

                // Load missing users into Redis for future checks
                for (const userId of missingUsers) {
                    if (dbEligible.has(userId)) {
                        await this.loadUserCreditsFromDB(userId);
                    }
                }
            }
            
            console.log(`💳 Batch check: ${eligibleUsers.size}/${userIds.length} users have sufficient credits`);

            // Debug logging for stress testing
            if (process.env.NODE_ENV === 'production' && userIds.length > 10) {
                console.log(`🔍 Redis batch check details: required=${requiredCredits}, eligible=${eligibleUsers.size}/${userIds.length}`);

                // Show first few ineligible users for debugging
                if (eligibleUsers.size < userIds.length) {
                    const ineligibleUsers = userIds.filter(userId => !eligibleUsers.has(userId));
                    console.log(`⚠️ First 3 ineligible users: ${ineligibleUsers.slice(0, 3).join(', ')}`);
                }
            }
            
        } catch (error) {
            console.error('❌ Error in batch credit check:', error);
            this.stats.fallbackOperations++;
            
            if (this.fallbackToDatabase) {
                return await this.batchCheckCreditsFromDB(userIds, requiredCredits);
            }
        }
        
        return eligibleUsers;
    }

    /**
     * Load specific user credits from database to Redis
     */
    async loadUserCreditsFromDB(userId) {
        try {
            const result = await query(`
                SELECT credits_remaining, credits_used_this_month, tier_id
                FROM users 
                WHERE id = $1 AND is_active = true
            `, [userId]);

            if (result.rows.length === 0) {
                return false;
            }

            const user = result.rows[0];
            const creditKey = `${this.keyPrefix}${userId}`;
            
            await redis.hset(creditKey, {
                remaining: user.credits_remaining,
                used_this_month: user.credits_used_this_month,
                tier_id: user.tier_id,
                last_updated: Date.now()
            });
            
            await redis.expire(creditKey, this.creditTTL);
            return true;
            
        } catch (error) {
            console.error('❌ Error loading user credits from DB:', error);
            return false;
        }
    }

    /**
     * Fallback credit check using database
     */
    async checkCreditsFromDB(userId, requiredCredits) {
        try {
            const result = await query(`
                SELECT credits_remaining 
                FROM users 
                WHERE id = $1 AND is_active = true
            `, [userId]);

            if (result.rows.length === 0) {
                return false;
            }

            return result.rows[0].credits_remaining >= requiredCredits;
            
        } catch (error) {
            console.error('❌ Error checking credits from DB:', error);
            return false;
        }
    }

    /**
     * Fallback credit consumption using database
     */
    async consumeCreditsFromDB(userId, creditsToConsume, metadata) {
        try {
            const result = await query(
                'SELECT consume_credits($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)',
                [
                    userId,
                    metadata.endpoint || 'websocket://stream',
                    'STREAM',
                    creditsToConsume,
                    200,
                    null,
                    null,
                    'WebSocket Stream Client',
                    JSON.stringify(metadata),
                    null
                ]
            );

            return result.rows[0].consume_credits;
        } catch (error) {
            console.error('❌ Error consuming credits from DB:', error);
            return false;
        }
    }

    /**
     * Fallback batch credit check using database
     */
    async batchCheckCreditsFromDB(userIds, requiredCredits) {
        const eligibleUsers = new Set();
        
        try {
            const placeholders = userIds.map((_, index) => `$${index + 1}`).join(',');
            const result = await query(
                `SELECT u.id, u.credits_remaining, t.max_credits_per_month
                 FROM users u
                 JOIN access_tiers t ON u.tier_id = t.id
                 WHERE u.id IN (${placeholders}) AND u.is_active = true`,
                userIds
            );

            for (const row of result.rows) {
                const userId = row.id;
                const creditsRemaining = row.credits_remaining;
                const maxCreditsPerMonth = row.max_credits_per_month;

                // Enterprise tier (unlimited credits) always gets access
                if (maxCreditsPerMonth === -1) {
                    eligibleUsers.add(userId);
                } else {
                    // Other tiers need sufficient credits
                    if (creditsRemaining >= requiredCredits) {
                        eligibleUsers.add(userId);
                    }
                }
            }
        } catch (error) {
            console.error('❌ Error in database batch credit check:', error);
        }
        
        return eligibleUsers;
    }

    /**
     * Start periodic synchronization with PostgreSQL
     */
    startPeriodicSync() {
        if (!this.enabled || this.syncIntervalId) {
            return;
        }

        this.syncIntervalId = setInterval(() => {
            this.syncToDatabase().catch(error => {
                console.error('❌ Error in periodic sync:', error);
                this.stats.syncErrors++;
            });
        }, this.syncInterval);

        console.log('✅ Periodic sync started');
    }

    /**
     * Synchronize Redis changes to PostgreSQL
     */
    async syncToDatabase() {
        if (!this.enabled) {
            return;
        }

        try {
            // Get sync queue items
            const syncItems = await redis.lrange(this.syncQueueKey, 0, this.maxSyncBatchSize - 1);
            
            if (syncItems.length === 0) {
                return;
            }

            console.log(`🔄 Syncing ${syncItems.length} credit operations to database...`);

            // Process sync items
            const userUpdates = new Map();
            
            for (const item of syncItems) {
                try {
                    const syncData = JSON.parse(item);
                    const { userId, creditsConsumed } = syncData;
                    
                    if (!userUpdates.has(userId)) {
                        userUpdates.set(userId, 0);
                    }
                    userUpdates.set(userId, userUpdates.get(userId) + creditsConsumed);
                    
                } catch (parseError) {
                    console.error('❌ Error parsing sync item:', parseError);
                }
            }

            // Update database
            for (const [userId, totalCredits] of userUpdates) {
                await query(`
                    UPDATE users 
                    SET credits_remaining = credits_remaining - $1,
                        credits_used_this_month = credits_used_this_month + $1
                    WHERE id = $2
                `, [totalCredits, userId]);
            }

            // Remove processed items from queue
            await redis.ltrim(this.syncQueueKey, syncItems.length, -1);

            // Update stats
            this.stats.totalSyncs++;
            this.stats.lastSyncTime = new Date();
            this.stats.lastSyncSize = syncItems.length;

            console.log(`✅ Synced ${userUpdates.size} users, ${syncItems.length} operations to database`);
            
        } catch (error) {
            console.error('❌ Database sync failed:', error);
            this.stats.syncErrors++;
            throw error;
        }
    }

    /**
     * Stop the credit tracker
     */
    async stop() {
        if (this.syncIntervalId) {
            clearInterval(this.syncIntervalId);
            this.syncIntervalId = null;
        }

        if (this.enabled) {
            // Final sync
            await this.syncToDatabase();
        }
        
        console.log('🛑 RedisCreditTracker stopped');
    }

    /**
     * Get tracker statistics
     */
    getStats() {
        return {
            ...this.stats,
            isInitialized: this.isInitialized,
            isRunning: this.syncIntervalId !== null,
            enabled: this.enabled
        };
    }

    /**
     * Force sync to database (for testing or manual triggers)
     */
    async flush() {
        if (this.enabled) {
            await this.syncToDatabase();
        }
    }
}

// Create singleton instance
export const redisCreditTracker = new RedisCreditTracker();

// Auto-initialize based on configuration
const config = getRedisConfig();
if (config.enabled) {
    redisCreditTracker.init().catch(error => {
        console.error('❌ Failed to initialize RedisCreditTracker:', error);
    });
}

// Graceful shutdown
process.on('SIGTERM', async () => {
    console.log('🔄 Gracefully shutting down RedisCreditTracker...');
    await redisCreditTracker.stop();
});

process.on('SIGINT', async () => {
    console.log('🔄 Gracefully shutting down RedisCreditTracker...');
    await redisCreditTracker.stop();
});
