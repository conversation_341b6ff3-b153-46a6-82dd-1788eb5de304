import { query } from '../config/database.js';
import { cache } from '../config/redis.js';
import { redisCreditTracker } from '../services/RedisCreditTracker.js';
import { getRedisConfig, getBatchConfig } from '../config/performance.js';

// WebSocket Stream Credit Management with Performance Optimizations
export class StreamCreditManager {
    constructor() {
        this.streamDefinitions = new Map();
        this.loadStreamDefinitions();

        // Get performance configuration
        this.redisConfig = getRedisConfig();
        this.batchConfig = getBatchConfig();

        // Performance configuration
        this.useRedisTracking = this.redisConfig.enabled;
        this.useBatchProcessing = this.batchConfig.enabled;

        console.log('🔄 StreamCreditManager initialized with:', {
            redisTracking: this.useRedisTracking,
            batchProcessing: this.useBatchProcessing
        });

        // Refresh stream definitions every 5 minutes
        setInterval(() => {
            this.loadStreamDefinitions();
        }, 5 * 60 * 1000);
    }

    // Load stream definitions from database
    async loadStreamDefinitions() {
        try {
            const result = await query(
                'SELECT stream_name, credits_per_message, required_tier_id, is_active FROM stream_definitions WHERE is_active = true'
            );
            
            this.streamDefinitions.clear();
            for (const row of result.rows) {
                this.streamDefinitions.set(row.stream_name, {
                    creditsPerMessage: row.credits_per_message,
                    requiredTierId: row.required_tier_id,
                    isActive: row.is_active
                });
            }
            
            console.log(`✅ Loaded ${result.rows.length} stream definitions for credit management`);
        } catch (error) {
            console.error('❌ Error loading stream definitions:', error);
        }
    }

    // Get credit cost for a stream (without user context)
    getStreamCreditCost(streamName) {
        const streamDef = this.streamDefinitions.get(streamName);
        return streamDef ? streamDef.creditsPerMessage : 1; // Default to 1 credit
    }

    // Get credit cost for a stream for a specific user (considers tier)
    async getUserStreamCreditCost(userId, streamName) {
        try {
            // Check if this is an admin user (admin user IDs start with 'admin_')
            if (typeof userId === 'string' && userId.startsWith('admin_')) {
                console.log(`💳 Admin user ${userId} - no credits required for stream ${streamName}`);
                return 0; // Admin users always pay 0 credits
            }

            // Get user's tier information
            const cacheKey = `user:${userId}`;
            let user = await cache.get(cacheKey);

            if (!user) {
                // Fallback to database if not in cache
                const result = await query(
                    'SELECT u.credits_remaining, u.tier_id, t.max_credits_per_month FROM users u JOIN access_tiers t ON u.tier_id = t.id WHERE u.id = $1 AND u.is_active = true',
                    [userId]
                );

                if (result.rows.length === 0) {
                    return this.getStreamCreditCost(streamName); // Fallback to default cost
                }

                user = {
                    credits_remaining: result.rows[0].credits_remaining,
                    tier_id: result.rows[0].tier_id,
                    max_credits_per_month: result.rows[0].max_credits_per_month
                };
            }

            // Enterprise tier (unlimited credits) gets 0 cost for all streams
            if (user.max_credits_per_month === -1) {
                return 0;
            }

            // For other tiers, return the standard stream cost
            return this.getStreamCreditCost(streamName);
        } catch (error) {
            console.error('❌ Error getting user stream credit cost:', error);
            return this.getStreamCreditCost(streamName); // Fallback to default cost
        }
    }

    // Check if user has sufficient credits for a stream message
    async checkUserCredits(userId, streamName) {
        try {
            // Check if this is an admin user (admin user IDs start with 'admin_')
            if (typeof userId === 'string' && userId.startsWith('admin_')) {
                console.log(`💳 Admin user ${userId} - unlimited access to stream ${streamName}`);
                return true; // Admin users always have access
            }

            const creditCost = await this.getUserStreamCreditCost(userId, streamName);

            // If cost is 0 (e.g., Enterprise tier), always allow
            if (creditCost === 0) {
                return true;
            }

            // Get user's current credits from cache first
            const cacheKey = `user:${userId}`;
            let user = await cache.get(cacheKey);

            if (!user) {
                // Fallback to database if not in cache
                const result = await query(
                    'SELECT credits_remaining, tier_id FROM users WHERE id = $1 AND is_active = true',
                    [userId]
                );

                if (result.rows.length === 0) {
                    return false;
                }

                user = {
                    credits_remaining: result.rows[0].credits_remaining,
                    tier_id: result.rows[0].tier_id
                };
            }

            return user.credits_remaining >= creditCost;
        } catch (error) {
            console.error('❌ Error checking user credits:', error);
            return false;
        }
    }

    // Consume credits for a stream message (optimized for high-frequency operations)
    async consumeStreamCredits(userId, streamName, connectionId = null) {
        try {
            const creditCost = await this.getUserStreamCreditCost(userId, streamName);

            if (creditCost === 0) {
                console.log(`💳 No credits required for user ${userId} on stream ${streamName} (Enterprise tier)`);
                return true; // No credits required
            }

            // Use optimized credit processing based on configuration
            if (this.useRedisTracking) {
                // High-performance Redis-based credit tracking
                return await this.consumeCreditsRedis(userId, streamName, creditCost, connectionId);
            } else {
                // Traditional database approach (fallback)
                return await this.consumeCreditsDatabase(userId, streamName, creditCost, connectionId);
            }

        } catch (error) {
            console.error('❌ Error consuming stream credits:', error);
            return false;
        }
    }

    // Redis-based credit consumption (highest performance)
    async consumeCreditsRedis(userId, streamName, creditCost, connectionId) {
        try {
            const metadata = {
                streamName,
                connectionId,
                endpoint: `websocket://${streamName}`
            };
            const success = await redisCreditTracker.consumeCredits(userId, creditCost, metadata);

            if (success) {
                // Optimistically update cache
                await this.updateCacheOptimistically(userId, creditCost);
                console.log(`💳 [Redis] Consumed ${creditCost} credits for user ${userId} on stream ${streamName}`);
            } else {
                console.warn(`⚠️ [Redis] Failed to consume credits for user ${userId} on stream ${streamName}`);
            }

            return success;
        } catch (error) {
            console.error('❌ Redis credit consumption failed, falling back to database:', error);
            return await this.consumeCreditsDatabase(userId, streamName, creditCost, connectionId);
        }
    }

    // Traditional database credit consumption (fallback)
    async consumeCreditsDatabase(userId, streamName, creditCost, connectionId) {
        try {
            // Use database function to consume credits atomically
            const result = await query(
                'SELECT consume_credits($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)',
                [
                    userId,
                    `websocket://${streamName}`,
                    'STREAM',
                    creditCost,
                    200, // success status
                    null, // response time
                    null, // ip address
                    'WebSocket Stream Client',
                    JSON.stringify({ stream: streamName, connectionId }),
                    null // response payload
                ]
            );

            const success = result.rows[0].consume_credits;

            if (success) {
                // Update cached user data
                const cacheKey = `user:${userId}`;
                const cachedUser = await cache.get(cacheKey);
                if (cachedUser) {
                    cachedUser.credits_remaining -= creditCost;
                    cachedUser.credits_used_this_month += creditCost;
                    await cache.set(cacheKey, cachedUser, 300);
                }

                console.log(`💳 [Database] Consumed ${creditCost} credits for user ${userId} on stream ${streamName}`);
            } else {
                console.warn(`⚠️ [Database] Failed to consume credits for user ${userId} on stream ${streamName}`);
            }

            return success;
        } catch (error) {
            console.error('❌ Database credit consumption failed:', error);
            return false;
        }
    }

    // Optimistically update cache for Redis operations
    async updateCacheOptimistically(userId, creditCost) {
        try {
            const cacheKey = `user:${userId}`;
            const cachedUser = await cache.get(cacheKey);
            if (cachedUser) {
                cachedUser.credits_remaining -= creditCost;
                cachedUser.credits_used_this_month += creditCost;
                await cache.set(cacheKey, cachedUser, 300);
            }
        } catch (error) {
            console.error('❌ Cache update failed:', error);
            // Don't throw - cache update failure shouldn't fail the operation
        }
    }

    // Batch check credits for multiple users (optimized for high-frequency operations)
    async batchCheckCredits(userIds, streamName) {
        const eligibleUsers = new Set();

        try {
            // Separate admin users from regular users
            const adminUsers = userIds.filter(id => typeof id === 'string' && id.startsWith('admin_'));
            const regularUsers = userIds.filter(id => !(typeof id === 'string' && id.startsWith('admin_')));

            // Admin users always have access
            adminUsers.forEach(adminId => {
                eligibleUsers.add(adminId);
                console.log(`💳 Admin user ${adminId} - unlimited access to stream ${streamName}`);
            });

            // Check regular users if any
            if (regularUsers.length > 0) {
                const streamCreditCost = this.getStreamCreditCost(streamName);

                if (this.useRedisTracking) {
                    // High-performance Redis-based batch check
                    const redisEligible = await redisCreditTracker.batchCheckCredits(regularUsers, streamCreditCost);
                    redisEligible.forEach(userId => eligibleUsers.add(userId));
                } else {
                    // Traditional database batch check
                    const dbEligible = await this.batchCheckCreditsDatabase(regularUsers, streamCreditCost);
                    dbEligible.forEach(userId => eligibleUsers.add(userId));
                }
            }

            console.log(`💳 ${eligibleUsers.size}/${userIds.length} users have sufficient credits for ${streamName}`);
        } catch (error) {
            console.error('❌ Error batch checking credits:', error);
        }

        return eligibleUsers;
    }

    // Database-based batch credit check (fallback method)
    async batchCheckCreditsDatabase(userIds, streamCreditCost) {
        const eligibleUsers = new Set();

        try {
            const placeholders = userIds.map((_, index) => `$${index + 1}`).join(',');
            const result = await query(
                `SELECT u.id, u.credits_remaining, t.max_credits_per_month
                 FROM users u
                 JOIN access_tiers t ON u.tier_id = t.id
                 WHERE u.id IN (${placeholders}) AND u.is_active = true`,
                userIds
            );

            for (const row of result.rows) {
                const userId = row.id;
                const creditsRemaining = row.credits_remaining;
                const maxCreditsPerMonth = row.max_credits_per_month;

                // Enterprise tier (unlimited credits) always gets access
                if (maxCreditsPerMonth === -1) {
                    eligibleUsers.add(userId);
                } else {
                    // Other tiers need sufficient credits
                    if (creditsRemaining >= streamCreditCost) {
                        eligibleUsers.add(userId);
                    }
                }
            }
        } catch (error) {
            console.error('❌ Error in database batch credit check:', error);
        }

        return eligibleUsers;
    }

    // Get stream statistics
    async getStreamStats(streamName, days = 7) {
        try {
            const result = await query(
                `SELECT 
                    COUNT(*) as total_messages,
                    SUM(credits_amount) as total_credits_consumed,
                    COUNT(DISTINCT user_id) as unique_users
                 FROM api_usage_logs 
                 WHERE endpoint = $1 AND created_at >= CURRENT_DATE - INTERVAL '${parseInt(days)} days'`,
                [`websocket:/${streamName}`]
            );

            return result.rows[0] || { total_messages: 0, total_credits_consumed: 0, unique_users: 0 };
        } catch (error) {
            console.error('❌ Error getting stream stats:', error);
            return { total_messages: 0, total_credits_consumed: 0, unique_users: 0 };
        }
    }
}

// Global instance
export const streamCreditManager = new StreamCreditManager();

// Helper function to check if stream credits are enabled
export const isStreamCreditsEnabled = () => {
    return process.env.STREAM_CREDITS_ENABLED !== 'false';
};
