import { redis } from "../config/redis.js";
import { pubsub } from "../config/redis.js";
import { query } from "../config/database.js";
import { KolFeed } from "../workers/kolFeed.js";
import { KafkaStreams } from "../workers/kafkaStream.js";
import { SolanaTracker } from "../workers/solanaTracker.js";

/**
 * Stream Manager Class
 *
 * Manages all real-time data streams including KOL feed, Kafka streams, and SolanaTracker streams.
 * Handles stream registration, worker initialization, Redis pub/sub subscriptions, and automatic
 * subscription management for WebSocket clients. Implements memory leak prevention and maintenance.
 */
export class StreamManager {
  /**
   * Initialize Stream Manager
   * Sets up stream tracking maps and initializes all available streams
   */
  constructor() {
    this.streams = new Map(); // Stream configurations
    this.intervals = new Map(); // Interval timers for polling-based streams
    this.isRunning = false; // Manager state
    this.kolFeed = null; // KOL feed worker instance
    this.kafkaStreams = null; // Kafka streams worker instance
    this.solanaTracker = null; // SolanaTracker worker instance

    this.initializeStreams();
  }

  /**
   * Initialize all available streams and clean up previous sessions
   * Registers all stream types and clears any stale connection data
   */
  initializeStreams() {
    (async () => {
      // Clean up any stale WebSocket sessions from previous runs
      await query(
        "UPDATE websocket_sessions SET disconnected_at = NOW() WHERE disconnected_at IS NULL"
      );

      // Clear Redis connection tracking keys to prevent memory leaks
      const keys = await redis.keys('ws_connections:*');
      if (keys.length > 0) {
        await redis.del(...keys);
        console.log(`✅ Cleared ${keys.length} ws_connections cache keys`);
      } else {
        console.log('ℹ️ No ws_connections cache keys found');
      }
    })();

    Promise.all([
      // KOL Feed stream - real-time KOL trading activity
      this.registerStream("kol-feed", {
        isEventDriven: true,
      }),
      // Jupiter AMM swaps stream - real-time Jupiter AMM swaps
      this.registerStream("jupiter-amm-swaps", {
        isEventDriven: true,
      }),
      // Pumpfun AMM swaps stream - real-time Pumpfun AMM swaps
      this.registerStream("pumpfun-amm-swaps", {
        isEventDriven: true,
      }),
      // Jupiter DCA orders stream - real-time Jupiter DCA orders
      this.registerStream("jupiter-dca-orders", {
        isEventDriven: true,
      }),
      // SolanaTracker streams - real-time Solana data from SolanaTracker API
      this.registerStream("tokens-launched", {
        isEventDriven: true,
      }),
      this.registerStream("tokens-graduating", {
        isEventDriven: true,
      }),
      this.registerStream("tokens-graduated", {
        isEventDriven: true,
      }),
      this.registerStream("pool-changes", {
        isEventDriven: true,
      }),
      this.registerStream("token-transactions", {
        isEventDriven: true,
      }),
      this.registerStream("price-updates", {
        isEventDriven: true,
      }),
      this.registerStream("wallet-transactions", {
        isEventDriven: true,
      }),
      this.registerStream("token-holders", {
        isEventDriven: true,
      }),
      this.registerStream("token-changes", {
        isEventDriven: true,
      }),
    ])


    console.log("✅ Stream Manager initialized with all streams");
  }

  // Register a new stream
  registerStream(streamName, config) {
    this.streams.set(streamName, {
      name: streamName,
      interval: config.interval || 0,
      generator: config.generator || null,
      isEventDriven: config.isEventDriven || false,
      isActive: false,
      lastUpdate: null,
      messageCount: 0,
    });
  }

  // Start all streams
  async start() {
    if (this.isRunning) {
      console.log("Stream Manager is already running");
      return;
    }

    this.isRunning = true;

    const [kolFeed, kafkaStreams, solanaTracker] = await Promise.all([
      new KolFeed(),
      new KafkaStreams(),
      new SolanaTracker(),
    ]);

    // Initialize workers
    this.kolFeed = kolFeed;
    this.kafkaStreams = kafkaStreams;
    this.solanaTracker = solanaTracker;

    await Promise.all([
      this.kolFeed.init(),
      this.kafkaStreams.init(),
      this.solanaTracker.init(),
    ])

    // Set up Redis subscriptions for internal messages
    this.setupKolFeedSubscription();
    this.setupKafkaStreamsSubscription();
    this.setupSolanaTrackerSubscription();
    this.setupStreamSubscriptionHandler();

    // Setup periodic maintenance for memory leak prevention
    this.setupMaintenanceScheduler();

    console.log("✅ All workers initialized and connected");

    // Start all streams
    for (const [streamName, stream] of this.streams) {
      if (stream.isEventDriven) {
        // Mark event-driven streams as active
        stream.isActive = true;
        console.log(`✅ Event-driven stream started: ${streamName}`);
      } else if (stream.generator) {
        // Start interval-based streams
        this.startIntervalStream(streamName);
      } else {
        console.warn(
          `⚠️ Stream ${streamName} has no generator and is not event-driven`
        );
      }
    }

    console.log("✅ All streams started");
  }

  // Stop all streams
  async stop() {
    if (!this.isRunning) {
      console.log("Stream Manager is not running");
      return;
    }

    this.isRunning = false;

    // Stop maintenance scheduler
    if (this.maintenanceInterval) {
      clearInterval(this.maintenanceInterval);
      this.maintenanceInterval = null;
      console.log("🛑 Maintenance scheduler stopped");
    }

    // Stop all workers
    if (this.kolFeed) {
      this.kolFeed.stop();
      this.kolFeed = null;
    }

    if (this.kafkaStreams) {
      this.kafkaStreams.stop();
      this.kafkaStreams = null;
    }

    if (this.solanaTracker) {
      this.solanaTracker.stop();
      this.solanaTracker = null;
    }

    // Stop all interval-based streams
    for (const [streamName, stream] of this.streams) {
      if (!stream.isActive) {
        console.log(`Stream ${streamName} is not active`);
        continue;
      }

      this.stopIntervalStream(streamName);
    }

    console.log("🛑 All streams stopped");
  }

  // Start an interval-based stream
  startIntervalStream(streamName) {
    const stream = this.streams.get(streamName);
    if (!stream) {
      console.error(`Stream not found: ${streamName}`);
      return;
    }

    if (stream.isActive) {
      console.log(`Stream ${streamName} is already active`);
      return;
    }

    if (stream.isEventDriven) {
      console.log(
        `Cannot start event-driven stream ${streamName} with interval`
      );
      return;
    }

    const interval = setInterval(async () => {
      try {
        const data = await stream.generator();
        await this.publishStreamData(streamName, data);

        stream.lastUpdate = Date.now();
        stream.messageCount++;
      } catch (error) {
        console.error(`Error generating data for stream ${streamName}:`, error);
      }
    }, stream.interval);

    this.intervals.set(streamName, interval);
    stream.isActive = true;

    console.log(
      `✅ Interval stream started: ${streamName} (interval: ${stream.interval}ms)`
    );
  }

  // Stop an interval-based stream
  stopIntervalStream(streamName) {
    const stream = this.streams.get(streamName);
    if (!stream) {
      console.error(`Stream not found: ${streamName}`);
      return;
    }

    if (!stream.isActive) {
      console.log(`Stream ${streamName} is not active`);
      return;
    }

    const interval = this.intervals.get(streamName);
    if (interval) {
      clearInterval(interval);
      this.intervals.delete(streamName);
    }

    stream.isActive = false;
    console.log(`🛑 Interval stream stopped: ${streamName}`);
  }

  // Set up Redis subscription for KOL feed internal messages
  setupKolFeedSubscription() {
    // Subscribe to the internal KOL feed channel
    pubsub.subscribe("kol_feed_internal", (data) => {
      try {
        // Debug logging for production
        if (process.env.NODE_ENV === 'production') {
          console.log("📥 StreamManager: Received KOL feed internal message");
        }

        // Handle the KOL feed data through the event-driven system
        this.handleEventDrivenData("kol-feed", data);
      } catch (error) {
        console.error("❌ Error handling KOL feed internal message:", error);
      }
    });

    console.log("✅ KOL Feed Redis subscription setup");
  }

  setupKafkaStreamsSubscription() {
    // Subscribe to the internal Kafka streams channel
    pubsub.subscribe("kafka_streams_internal", (data) => {
      try {
        // Handle the Kafka streams data through the event-driven system
        this.handleEventDrivenData(data.stream, data.data);
      } catch (error) {
        console.error("❌ Error handling Kafka Streams internal message:", error);
      }
    });

    console.log("✅ Kafka Streams Redis subscription setup");
  }

  setupSolanaTrackerSubscription() {
    // Subscribe to the internal SolanaTracker channel
    pubsub.subscribe("solana_tracker_internal", (data) => {
      try {
        // Map room types to stream names (handle parameterized rooms)
        const roomToStreamMap = {
          'latest': 'tokens-launched',
          'graduating': 'tokens-graduating',
          'graduated': 'tokens-graduated',
          'pool': 'pool-changes',
          'transaction': 'token-transactions',
          'price': 'price-updates',
          'wallet': 'wallet-transactions',
          'holders': 'token-holders',
          'token': 'token-changes'
        };

        // Extract base room type (handle parameterized rooms like graduating:sol:175)
        const roomType = data.room.split(':')[0];
        const streamName = roomToStreamMap[roomType];

        if (streamName) {
          // Handle the SolanaTracker data through the event-driven system
          this.handleEventDrivenData(streamName, data.data);
        } else {
          console.warn(`⚠️ [StreamManager] Unknown SolanaTracker room type: ${roomType} (full room: ${data.room})`);
        }
      } catch (error) {
        console.error("❌ Error handling SolanaTracker internal message:", error);
      }
    });

    console.log("✅ SolanaTracker Redis subscription setup");
  }

  setupStreamSubscriptionHandler() {
    // Subscribe to stream subscription events to trigger SolanaTracker worker subscriptions
    pubsub.subscribe("stream_subscription", async (data) => {
      try {
        const { type, stream, connectionId, parameters } = data;

        // Map stream names to SolanaTracker rooms with parameter support
        let room = null;

        switch (stream) {
          case 'tokens-launched': // DONE
            room = 'latest';
            break;
          case 'tokens-graduating': // DONE
            // Support parameterized graduating room: graduating:sol:175
            if (parameters && parameters.market_cap) {
              room = `graduating:sol:${parameters.market_cap}`;
            } else {
              room = 'graduating';
            }
            break;
          case 'tokens-graduated': // DONE
            room = 'graduated';
            break;
          case 'pool-changes': // DONE
            if (parameters && parameters.pool_id) {
              room = `pool:${parameters.pool_id}`;
            } else {
              room = 'pool';
            }
            break;
          case 'token-transactions': // DONE
            if (parameters && parameters.token) {
              room = `transaction:${parameters.token}`;
            } else {
              room = 'transaction';
            }
            break;
          case 'price-updates': // DONE
            if (parameters && parameters.token) {
              room = `price:${parameters.token}`;
            } else {
              room = 'price';
            }
            break;
          case 'wallet-transactions': // DONE
            if (parameters && parameters.wallet) {
              room = `wallet:${parameters.wallet}`;
            } else {
              room = 'wallet';
            }
            break;
          case 'token-holders': // DONE
            if (parameters && parameters.token) {
              room = `holders:${parameters.token}`;
            } else {
              room = 'holders';
            }
            break;
          case 'token-changes': // DONE
            if (parameters && parameters.token) {
              room = `token:${parameters.token}`;
            } else {
              room = 'token';
            }
            break;
          default:
            // Not a SolanaTracker stream, ignore
            return;
        }

        if (type === 'subscribe') {
          // Subscribe to SolanaTracker room if not already subscribed
          try {
            await this.subscribeSolanaTrackerRoom(room, connectionId);
            console.log(`✅ [StreamManager] Auto-subscribed to SolanaTracker room '${room}' for stream '${stream}' (user: ${connectionId})`);
          } catch (error) {
            console.error(`❌ [StreamManager] Failed to auto-subscribe to SolanaTracker room '${room}':`, error.message);
          }
        } else if (type === 'unsubscribe') {
          // Check if this is a bulk disconnect (client_disconnect reason)
          const isClientDisconnect = data.reason === 'client_disconnect';

          if (isClientDisconnect) {
            // For client disconnects, use bulk unsubscribe to clean up all rooms at once
            // But only do this once per connectionId to avoid duplicate cleanup
            if (!this.disconnectCleanupTracker) {
              this.disconnectCleanupTracker = new Set();
            }

            if (!this.disconnectCleanupTracker.has(connectionId)) {
              this.disconnectCleanupTracker.add(connectionId);

              try {
                const result = await this.bulkUnsubscribeSolanaTracker(connectionId);
                console.log(`✅ [StreamManager] Bulk cleanup completed for disconnected user ${connectionId}: ${result.totalRooms} rooms processed`);

                // Clean up tracker after a delay to prevent memory leaks
                setTimeout(() => {
                  this.disconnectCleanupTracker?.delete(connectionId);
                }, 30000); // 30 seconds

              } catch (error) {
                console.error(`❌ [StreamManager] Failed to bulk cleanup for disconnected user ${connectionId}:`, error.message);
                this.disconnectCleanupTracker.delete(connectionId);
              }
            }
          } else {
            // Regular unsubscribe from specific room
            try {
              await this.unsubscribeSolanaTrackerRoom(room, connectionId);
              console.log(`✅ [StreamManager] Auto-unsubscribed from SolanaTracker room '${room}' for stream '${stream}' (user: ${connectionId})`);
            } catch (error) {
              console.error(`❌ [StreamManager] Failed to auto-unsubscribe from SolanaTracker room '${room}':`, error.message);
            }
          }
        }
      } catch (error) {
        console.error("❌ Error handling stream subscription event:", error);
      }
    });

    console.log("✅ Stream subscription handler setup");
  }

  // Setup periodic maintenance to prevent memory leaks
  setupMaintenanceScheduler() {
    // Run maintenance every 5 minutes
    this.maintenanceInterval = setInterval(() => {
      try {
        if (this.solanaTracker) {
          this.solanaTracker.performMaintenance();
        }
      } catch (error) {
        console.error("❌ Error during maintenance:", error);
      }
    }, 5 * 60 * 1000); // 5 minutes

    console.log("✅ Maintenance scheduler setup (5 minute intervals)");
  }

  // Handle event-driven data
  async handleEventDrivenData(streamName, data) {
    const stream = this.streams.get(streamName);
    if (!stream || !stream.isActive) {
      console.log(`⚠️ Stream ${streamName} not found or inactive`);
      return;
    }

    try {
      // Debug logging for production
      if (process.env.NODE_ENV === 'production') {
        console.log(`📤 StreamManager: Publishing ${streamName} data to stream_data channel`);
      }

      // Publish the data directly without additional wrapping
      await pubsub.publish("stream_data", {
        stream: streamName,
        data: data,
        timestamp: Date.now(),
      });
      stream.lastUpdate = Date.now();
      stream.messageCount++;

      // Debug logging for production
      if (process.env.NODE_ENV === 'production') {
        console.log(`✅ StreamManager: Successfully published ${streamName} data`);
      }
    } catch (error) {
      console.error(
        `Error handling event-driven data for stream ${streamName}:`,
        error
      );
    }
  }

  // Publish data to a stream via Redis
  async publishStreamData(streamName, data) {
    try {
      await pubsub.publish("stream_data", {
        stream: streamName,
        data,
        timestamp: Date.now(),
      });
    } catch (error) {
      console.error(`Error publishing to stream ${streamName}:`, error);
    }
  }



  // Get stream statistics
  getStreamStats() {
    const stats = {};

    for (const [streamName, stream] of this.streams) {
      stats[streamName] = {
        name: streamName,
        isActive: stream.isActive,
        interval: stream.interval,
        lastUpdate: stream.lastUpdate,
        messageCount: stream.messageCount,
        uptime:
          stream.isActive && stream.lastUpdate
            ? Date.now() - stream.lastUpdate
            : 0,
      };
    }

    return stats;
  }

  // Get list of available streams
  getAvailableStreams() {
    return Array.from(this.streams.keys());
  }

  // Check if stream exists
  streamExists(streamName) {
    return this.streams.has(streamName);
  }

  // Subscribe to SolanaTracker room (for dynamic subscriptions)
  async subscribeSolanaTrackerRoom(room, subscriberId) {
    if (!this.solanaTracker) {
      throw new Error("SolanaTracker worker is not initialized");
    }

    return await this.solanaTracker.subscribe(room, subscriberId);
  }

  // Unsubscribe from SolanaTracker room
  async unsubscribeSolanaTrackerRoom(room, subscriberId) {
    if (!this.solanaTracker) {
      throw new Error("SolanaTracker worker is not initialized");
    }

    return await this.solanaTracker.unsubscribe(room, subscriberId);
  }

  // Bulk unsubscribe from all SolanaTracker rooms (for disconnections)
  async bulkUnsubscribeSolanaTracker(subscriberId) {
    if (!this.solanaTracker) {
      throw new Error("SolanaTracker worker is not initialized");
    }

    return await this.solanaTracker.bulkUnsubscribe(subscriberId);
  }

  // Get SolanaTracker statistics
  getSolanaTrackerStats() {
    if (!this.solanaTracker) {
      return { error: "SolanaTracker worker is not initialized" };
    }

    return this.solanaTracker.getStats();
  }

  // Get available SolanaTracker room types
  getAvailableSolanaTrackerRooms() {
    if (!this.solanaTracker) {
      return [];
    }

    return this.solanaTracker.getAvailableRoomTypes();
  }


}
