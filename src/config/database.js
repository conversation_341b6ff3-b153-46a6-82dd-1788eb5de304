import pg from 'pg';
import dotenv from 'dotenv';
import { getConfig } from './performance.js';

dotenv.config();

const { Pool } = pg;

// Get performance configuration
const perfConfig = getConfig('database');

// Database configuration with performance optimizations
const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 5432,
    database: process.env.DB_NAME || 'api_engine',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || '',
    ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,

    // Performance-optimized connection pool settings
    max: perfConfig.maxConnections || 50,
    min: perfConfig.minConnections || 5,
    idleTimeoutMillis: perfConfig.idleTimeout || 30000,
    connectionTimeoutMillis: perfConfig.connectionTimeout || 5000,
    acquireTimeoutMillis: perfConfig.acquireTimeout || 10000,

    // Query performance settings
    statement_timeout: perfConfig.statementTimeout || 30000,
    query_timeout: perfConfig.statementTimeout || 30000,

    // Connection validation
    allowExitOnIdle: process.env.NODE_ENV !== 'production',
};

// Create connection pool
const pool = new Pool(dbConfig);

// Handle pool errors
pool.on('error', (err) => {
    console.error('Unexpected error on idle client', err);
    process.exit(-1);
});

// Test database connection
export const testConnection = async () => {
    try {
        const client = await pool.connect();
        const result = await client.query('SELECT NOW()');
        client.release();
        console.log('✅ Database connected successfully at:', result.rows[0].now);
        return true;
    } catch (err) {
        console.error('❌ Database connection failed:', err.message);
        return false;
    }
};

// Execute query with error handling
export const query = async (text, params) => {
    const start = Date.now();
    try {
        const result = await pool.query(text, params);
        const duration = Date.now() - start;
        
        // Log query execution in development mode for debugging
        if (process.env.NODE_ENV === 'development' && process.env.DEBUG_DATABASE === 'true') {
            console.log('Executed query', { text: text.substring(0, 100), duration, rows: result.rowCount });
        }
        
        return result;
    } catch (error) {
        console.error('Database query error:', error);
        throw error;
    }
};

// Get a client from the pool for transactions
export const getClient = async () => {
    return await pool.connect();
};

// Get pool statistics
export const getPoolStats = () => {
    return {
        totalCount: pool.totalCount,
        idleCount: pool.idleCount,
        waitingCount: pool.waitingCount,
        maxConnections: dbConfig.max,
        minConnections: dbConfig.min || 0,
        healthStatus: pool.totalCount > 0 ? 'healthy' : 'unhealthy'
    };
};

// Close the pool
export const closePool = async () => {
    await pool.end();
    console.log('Database pool closed');
};

export default pool;
