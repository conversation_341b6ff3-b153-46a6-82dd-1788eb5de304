/**
 * Performance Configuration
 * 
 * Centralized configuration for performance optimizations including
 * Redis credit tracking, batch processing, and background logging.
 */

export const performanceConfig = {
    // Redis Credit Tracking
    redis: {
        enabled: process.env.NODE_ENV === 'production', // Enable in production by default
        syncInterval: 30000, // 30 seconds - how often to sync Redis to PostgreSQL
        syncBatchSize: 500, // Maximum operations per sync batch
        keyPrefix: 'credits:', // Redis key prefix for credit data
        syncQueueKey: 'credit_sync_queue', // Queue for pending sync operations
        creditTTL: 86400, // 24 hours - TTL for credit data in Redis
        maxRetries: 3, // Maximum retry attempts for failed operations
        fallbackToDatabase: true // Fallback to database if <PERSON><PERSON> fails
    },

    // Batch Credit Processing
    batch: {
        enabled: process.env.NODE_ENV === 'production', // Enable in production by default
        interval: 5000, // 5 seconds - how often to process batches
        maxSize: 1000, // Maximum operations per batch
        maxRetries: 3, // Maximum retry attempts for failed batches
        enableOptimisticUpdates: true // Update cache optimistically
    },

    // Background Usage Logging
    logging: {
        enabled: process.env.NODE_ENV === 'production', // Enable in production by default
        flushInterval: 10000, // 10 seconds - how often to flush logs
        maxBatchSize: 1000, // Maximum logs per batch
        maxMemoryQueue: 5000, // Maximum logs in memory before persisting to Redis
        redisQueueKey: 'usage_log_queue', // Redis queue for usage logs
        enableBulkInserts: true, // Use bulk inserts for better performance
        enableFallback: true // Fallback to individual inserts if bulk fails
    },

    // Database Connection Pool
    database: {
        maxConnections: 50, // Increased from default 20
        minConnections: 5, // Minimum connections to maintain
        idleTimeout: 30000, // 30 seconds
        connectionTimeout: 5000, // 5 seconds
        acquireTimeout: 10000, // 10 seconds to acquire connection
        statementTimeout: 30000, // 30 seconds for query execution
        enablePoolMonitoring: process.env.NODE_ENV === 'development' // Monitor pool in dev
    },

    // Stream Credits
    streams: {
        enableCredits: true, // Enable credit system for streams
        enableBatchChecks: true, // Batch credit checks for multiple users
        cacheUserTiers: true, // Cache user tier information
        cacheTTL: 300, // 5 minutes cache TTL for user data
        enableOptimizations: process.env.NODE_ENV === 'production'
    },

    // Monitoring and Debugging
    monitoring: {
        enableStats: true, // Collect performance statistics
        statsInterval: 60000, // 1 minute - how often to log stats
        enableSlowQueryLogging: process.env.NODE_ENV === 'development',
        slowQueryThreshold: 1000, // 1 second threshold for slow queries
        enableMemoryMonitoring: true,
        memoryCheckInterval: 30000 // 30 seconds
    },

    // Feature Flags
    features: {
        enableRedisCredits: process.env.NODE_ENV === 'production',
        enableBatchProcessing: process.env.NODE_ENV === 'production',
        enableBackgroundLogging: process.env.NODE_ENV === 'production',
        enablePoolOptimizations: true,
        enableCaching: true
    }
};

/**
 * Get configuration for a specific component
 */
export function getConfig(component) {
    return performanceConfig[component] || {};
}

/**
 * Check if a feature is enabled
 */
export function isFeatureEnabled(feature) {
    return performanceConfig.features[feature] || false;
}

/**
 * Get Redis configuration
 */
export function getRedisConfig() {
    return {
        ...performanceConfig.redis,
        enabled: performanceConfig.features.enableRedisCredits
    };
}

/**
 * Get batch processing configuration
 */
export function getBatchConfig() {
    return {
        ...performanceConfig.batch,
        enabled: performanceConfig.features.enableBatchProcessing
    };
}

/**
 * Get logging configuration
 */
export function getLoggingConfig() {
    return {
        ...performanceConfig.logging,
        enabled: performanceConfig.features.enableBackgroundLogging
    };
}

/**
 * Override configuration (useful for testing)
 */
export function overrideConfig(component, overrides) {
    if (performanceConfig[component]) {
        performanceConfig[component] = {
            ...performanceConfig[component],
            ...overrides
        };
    }
}

/**
 * Enable all optimizations (for production)
 */
export function enableAllOptimizations() {
    performanceConfig.features = {
        enableRedisCredits: true,
        enableBatchProcessing: true,
        enableBackgroundLogging: true,
        enablePoolOptimizations: true,
        enableCaching: true
    };
    
    performanceConfig.redis.enabled = true;
    performanceConfig.batch.enabled = true;
    performanceConfig.logging.enabled = true;
}

/**
 * Disable all optimizations (for development/testing)
 */
export function disableAllOptimizations() {
    performanceConfig.features = {
        enableRedisCredits: false,
        enableBatchProcessing: false,
        enableBackgroundLogging: false,
        enablePoolOptimizations: false,
        enableCaching: false
    };
    
    performanceConfig.redis.enabled = false;
    performanceConfig.batch.enabled = false;
    performanceConfig.logging.enabled = false;
}

/**
 * Get performance mode based on environment
 */
export function getPerformanceMode() {
    if (process.env.NODE_ENV === 'production') {
        return 'high'; // All optimizations enabled
    } else if (process.env.NODE_ENV === 'staging') {
        return 'medium'; // Some optimizations enabled
    } else {
        return 'basic'; // Minimal optimizations
    }
}

/**
 * Configure performance based on mode
 */
export function setPerformanceMode(mode) {
    switch (mode) {
        case 'high':
            enableAllOptimizations();
            break;
        case 'medium':
            performanceConfig.features.enableBatchProcessing = true;
            performanceConfig.features.enableBackgroundLogging = true;
            performanceConfig.features.enableCaching = true;
            performanceConfig.batch.enabled = true;
            performanceConfig.logging.enabled = true;
            break;
        case 'basic':
        default:
            disableAllOptimizations();
            break;
    }
}

/**
 * Log current configuration (for debugging)
 */
export function logConfiguration() {
    const mode = getPerformanceMode();
    console.log('🔧 Performance Configuration:');
    console.log(`   Mode: ${mode}`);
    console.log(`   Redis Credits: ${performanceConfig.features.enableRedisCredits ? '✅' : '❌'}`);
    console.log(`   Batch Processing: ${performanceConfig.features.enableBatchProcessing ? '✅' : '❌'}`);
    console.log(`   Background Logging: ${performanceConfig.features.enableBackgroundLogging ? '✅' : '❌'}`);
    console.log(`   Pool Optimizations: ${performanceConfig.features.enablePoolOptimizations ? '✅' : '❌'}`);
    console.log(`   Caching: ${performanceConfig.features.enableCaching ? '✅' : '❌'}`);
}

// Initialize configuration based on environment
const mode = getPerformanceMode();
setPerformanceMode(mode);

// Log configuration on startup
if (process.env.NODE_ENV !== 'test') {
    logConfiguration();
}
