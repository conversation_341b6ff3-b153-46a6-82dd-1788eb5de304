#!/usr/bin/env node

import WebSocket from 'ws';
import { query } from './src/config/database.js';

const API_KEY = 'test_free_api_key_12345';
const WS_URL = 'ws://localhost:3001/ws';

async function testBatchCredits() {
    console.log('🧪 Testing Batch Credit Functionality for KOL Feed');
    console.log('==================================================');
    
    try {
        // Step 1: Get initial credit balance
        console.log('\n1. Getting initial credit balance...');
        const initialResult = await query(
            'SELECT credits_used_this_month, credits_remaining FROM users WHERE api_key = $1',
            [API_KEY]
        );
        
        if (initialResult.rows.length === 0) {
            throw new Error('User not found');
        }
        
        const initialCredits = initialResult.rows[0];
        console.log('   Initial Credits Used This Month:', initialCredits.credits_used_this_month);
        console.log('   Initial Credits Remaining:', initialCredits.credits_remaining);
        
        // Step 2: Connect to WebSocket and subscribe to kol-feed
        console.log('\n2. Connecting to WebSocket...');
        const ws = new WebSocket(`${WS_URL}?api_key=${API_KEY}`);
        
        let messageCount = 0;
        let subscribed = false;
        const messages = [];
        
        await new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Connection timeout'));
            }, 10000);
            
            ws.on('open', () => {
                console.log('   ✅ WebSocket connected');
                clearTimeout(timeout);
                resolve();
            });
            
            ws.on('error', (error) => {
                console.log('   ❌ WebSocket error:', error.message);
                clearTimeout(timeout);
                reject(error);
            });
        });
        
        // Step 3: Subscribe to kol-feed
        console.log('\n3. Subscribing to kol-feed stream...');
        ws.send(JSON.stringify({
            action: 'subscribe',
            stream: 'kol-feed'
        }));
        
        // Step 4: Collect messages for 15 seconds
        console.log('\n4. Collecting messages for 15 seconds...');
        await new Promise((resolve) => {
            const collectTimeout = setTimeout(() => {
                resolve();
            }, 15000);
            
            ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data.toString());
                    
                    if (message.type === 'subscribed' && message.stream === 'kol-feed') {
                        subscribed = true;
                        console.log('   ✅ Successfully subscribed to kol-feed');
                    } else if (message.type === 'stream_data' && message.stream === 'kol-feed') {
                        messageCount++;
                        messages.push(message);
                        
                        if (messageCount <= 5) {
                            console.log(`   📨 Message ${messageCount}: ${message.data.type} - ${message.data.kol_label}`);
                        } else if (messageCount === 6) {
                            console.log('   📨 ... (continuing to collect messages)');
                        }
                    }
                } catch (error) {
                    console.log('   ⚠️ Error parsing message:', error.message);
                }
            });
        });
        
        ws.close();
        
        console.log(`\n   📊 Total messages received: ${messageCount}`);
        
        if (!subscribed) {
            throw new Error('Failed to subscribe to kol-feed');
        }
        
        if (messageCount === 0) {
            console.log('   ⚠️ No messages received - this might be normal if no KOL activity');
        }
        
        // Step 5: Wait for batch processing (30 seconds + buffer)
        console.log('\n5. Waiting for batch credit processing (35 seconds)...');
        await new Promise(resolve => setTimeout(resolve, 35000));
        
        // Step 6: Check final credit balance
        console.log('\n6. Checking final credit balance...');
        const finalResult = await query(
            'SELECT credits_used_this_month, credits_remaining FROM users WHERE api_key = $1',
            [API_KEY]
        );
        
        const finalCredits = finalResult.rows[0];
        console.log('   Final Credits Used This Month:', finalCredits.credits_used_this_month);
        console.log('   Final Credits Remaining:', finalCredits.credits_remaining);

        // Step 7: Calculate credit consumption
        const creditsConsumed = finalCredits.credits_used_this_month - initialCredits.credits_used_this_month;
        console.log('\n7. Credit Analysis:');
        console.log('   Credits Consumed:', creditsConsumed);
        console.log('   Messages Received:', messageCount);
        
        if (messageCount > 0) {
            console.log('   Credits per Message:', (creditsConsumed / messageCount).toFixed(4));
        }
        
        // Step 8: Verify batch processing worked
        console.log('\n8. Batch Processing Verification:');
        if (messageCount > 0 && creditsConsumed > 0) {
            console.log('   ✅ Credits were consumed for received messages');
            console.log('   ✅ Batch credit processing appears to be working');
        } else if (messageCount > 0 && creditsConsumed === 0) {
            console.log('   ⚠️ Messages received but no credits consumed');
            console.log('   ⚠️ This might indicate batch processing delay or free tier');
        } else if (messageCount === 0) {
            console.log('   ℹ️ No messages received, so no credits should be consumed');
            console.log('   ℹ️ Test inconclusive - try again when KOL activity is higher');
        }
        
        // Step 9: Check Redis credit tracking
        console.log('\n9. Checking Redis credit tracking...');
        try {
            const redisResult = await query(
                "SELECT key, value FROM redis_cache WHERE key LIKE 'credits:%' AND key LIKE '%' || $1 || '%'",
                [API_KEY.substring(0, 10)]
            );
            
            if (redisResult.rows.length > 0) {
                console.log('   ✅ Redis credit tracking entries found:');
                redisResult.rows.forEach(row => {
                    console.log(`     ${row.key}: ${row.value}`);
                });
            } else {
                console.log('   ⚠️ No Redis credit tracking entries found');
            }
        } catch (error) {
            console.log('   ⚠️ Could not check Redis tracking:', error.message);
        }
        
        console.log('\n✅ Batch credit test completed successfully!');
        
    } catch (error) {
        console.error('\n❌ Test failed:', error.message);
        process.exit(1);
    }
}

testBatchCredits();
