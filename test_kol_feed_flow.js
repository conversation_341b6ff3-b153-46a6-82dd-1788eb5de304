#!/usr/bin/env node

import { pubsub } from './src/config/redis.js';
import WebSocket from 'ws';

async function testKolFeedFlow() {
    console.log('🔍 Testing Complete KOL Feed Flow');
    console.log('==================================');
    
    // Create 20 WebSocket connections to test the filtering issue
    const connections = [];
    const messageCount = {};
    const userIds = [];
    
    console.log('🔌 Creating 20 WebSocket connections...');
    
    for (let i = 1; i <= 20; i++) {
        const ws = new WebSocket('ws://localhost:3001/ws?apiKey=test_free_api_key_12345');
        messageCount[i] = 0;
        
        ws.on('open', () => {
            console.log(`   User ${i}: Connected`);
            // Subscribe to kol-feed
            ws.send(JSON.stringify({
                type: 'subscribe',
                payload: { stream: 'kol-feed' }
            }));
        });
        
        ws.on('message', (data) => {
            const message = JSON.parse(data);
            if (message.type === 'stream_data' && message.stream === 'kol-feed') {
                messageCount[i]++;
                console.log(`🎉 User ${i} received KOL feed message #${messageCount[i]}`);
            } else if (message.type === 'subscribed') {
                console.log(`   User ${i}: Subscribed to ${message.stream}`);
            } else if (message.type === 'user_info') {
                userIds[i] = message.user_id;
                console.log(`   User ${i}: ID = ${message.user_id}`);
            }
        });
        
        connections.push(ws);
    }
    
    // Wait for connections to establish and subscriptions to complete
    console.log('\n⏳ Waiting for connections and subscriptions...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('\n📤 Publishing test KOL feed message to kol_feed_internal...');
    
    // Create a test KOL feed message
    const testKolFeedData = {
        timestamp: Date.now(),
        type: "buy",
        kol_label: "TestKOL_Batch",
        wallet: "TestWallet_Batch_123",
        kol_avatar: "https://example.com/avatar.jpg",
        token_in: {
            symbol: "SOL",
            name: "Wrapped SOL",
            logo: "https://example.com/sol.png",
            amount: 2.5,
            amount_string: "2.5",
            amount_usd: 333,
            price: 133.33,
            mint: "So11111111111111111111111111111111111111112"
        },
        token_out: {
            symbol: "BATCH",
            name: "Batch Test Token",
            logo: "https://example.com/batch.png",
            amount: 2000000,
            amount_string: "2M",
            amount_usd: 333,
            price: 0.0001665,
            mint: "BatchTestMint123"
        },
        socials: [
            { type: "x", handle: "batchtest", followers: 5000 }
        ],
        signature: "BatchTestSignature123"
    };
    
    // Publish to the internal KOL feed channel
    await pubsub.publish('kol_feed_internal', testKolFeedData);
    console.log('✅ Test message published to kol_feed_internal channel');
    
    // Wait for message processing and delivery
    console.log('\n⏳ Waiting for message processing and delivery...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    console.log('\n📊 Results Analysis:');
    let totalMessages = 0;
    let usersWithMessages = 0;
    const usersWithMessagesIds = [];
    
    for (let i = 1; i <= 20; i++) {
        const count = messageCount[i];
        totalMessages += count;
        if (count > 0) {
            usersWithMessages++;
            usersWithMessagesIds.push(i);
        }
        console.log(`   User ${i}: ${count} messages (ID: ${userIds[i] || 'unknown'})`);
    }
    
    console.log(`\n📈 Summary:`);
    console.log(`   Total messages delivered: ${totalMessages}`);
    console.log(`   Users who received messages: ${usersWithMessages}/20`);
    console.log(`   Users with messages: [${usersWithMessagesIds.join(', ')}]`);
    console.log(`   Expected: All 20 users should receive 1 message each`);
    
    if (usersWithMessages === 20 && totalMessages === 20) {
        console.log('✅ SUCCESS: All users received messages - no filtering issue!');
    } else if (usersWithMessages > 0 && usersWithMessages < 20) {
        console.log('❌ FILTERING ISSUE CONFIRMED: Only some users received messages');
        console.log(`   This confirms the rate limiting/filtering problem you identified!`);
        console.log(`   Pattern: Users ${usersWithMessagesIds.join(', ')} received messages`);
        
        // Analyze the pattern
        if (usersWithMessagesIds.length <= 5) {
            console.log('   🔍 Analysis: Looks like a limit of ~5 users maximum');
        }
    } else if (usersWithMessages === 0) {
        console.log('❌ NO MESSAGES: No users received messages - check message flow');
    }
    
    // Close connections
    console.log('\n🔌 Closing connections...');
    connections.forEach(ws => ws.close());
    
    process.exit(0);
}

testKolFeedFlow().catch(console.error);
