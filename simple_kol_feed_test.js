#!/usr/bin/env node

import WebSocket from 'ws';

const API_KEY = 'test_free_api_key_12345';
const WS_URL = 'ws://localhost:3001/ws';

async function testKolFeedSubscription() {
    console.log('🧪 Simple KOL Feed Subscription Test');
    console.log('====================================');
    
    try {
        console.log('\n1. Connecting to WebSocket...');
        const ws = new WebSocket(`${WS_URL}?apiKey=${API_KEY}`);
        
        let messageCount = 0;
        let subscribed = false;
        
        ws.on('open', () => {
            console.log('   ✅ WebSocket connected');
            
            // Subscribe to kol-feed
            console.log('\n2. Subscribing to kol-feed stream...');
            ws.send(JSON.stringify({
                action: 'subscribe',
                stream: 'kol-feed'
            }));
        });
        
        ws.on('message', (data) => {
            try {
                const message = JSON.parse(data.toString());
                
                if (message.type === 'subscribed' && message.stream === 'kol-feed') {
                    subscribed = true;
                    console.log('   ✅ Successfully subscribed to kol-feed');
                    console.log('\n3. Listening for messages (will run for 30 seconds)...');
                } else if (message.type === 'stream_data' && message.stream === 'kol-feed') {
                    messageCount++;
                    console.log(`   📨 Message ${messageCount}: ${message.data.type} - ${message.data.kol_label || 'Unknown KOL'}`);
                } else if (message.type === 'error') {
                    console.log('   ❌ Error:', message.message);
                }
            } catch (error) {
                console.log('   ⚠️ Error parsing message:', error.message);
            }
        });
        
        ws.on('error', (error) => {
            console.log('   ❌ WebSocket error:', error.message);
        });
        
        ws.on('close', (code, reason) => {
            console.log(`   🔌 WebSocket closed: ${code} - ${reason}`);
        });
        
        // Run for 30 seconds
        setTimeout(() => {
            console.log(`\n4. Test completed after 30 seconds`);
            console.log(`   📊 Total messages received: ${messageCount}`);
            console.log(`   📊 Subscription status: ${subscribed ? 'Success' : 'Failed'}`);
            
            if (subscribed && messageCount > 0) {
                console.log('\n✅ Test successful! KOL feed is working and delivering messages.');
                console.log('💡 Check the server logs to see if batch credit processing is working.');
            } else if (subscribed && messageCount === 0) {
                console.log('\n⚠️ Subscription successful but no messages received.');
                console.log('💡 This might be normal if there is no KOL activity right now.');
            } else {
                console.log('\n❌ Test failed - could not subscribe to kol-feed.');
            }
            
            ws.close();
            process.exit(0);
        }, 30000);
        
    } catch (error) {
        console.error('\n❌ Test failed:', error.message);
        process.exit(1);
    }
}

testKolFeedSubscription();
