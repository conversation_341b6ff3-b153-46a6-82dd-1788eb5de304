{"name": "nodejs-api", "version": "1.0.0", "description": "Robust API Engine with Credit-based Usage Tracking and WebSocket Support", "type": "module", "main": "app.js", "scripts": {"setup-check": "node tests/setup-check.js", "dev": "NODE_ENV=development node --watch app.js", "start": "NODE_ENV=production node app.js", "test": "NODE_ENV=test node --test", "test:api": "node tests/test.js", "test:websocket": "node tests/websocket-test.js", "test:rate-limiting": "node tests/test-rate-limiting.js", "test:rate-limiting:full": "bash scripts/run-rate-limit-tests.sh", "test:stress": "node tests/run_stress_test.js", "test:stress:kol-feed": "NODE_ENV=production node tests/stress_test_kol_feed.js", "test:monitor-db": "node tests/monitor_database_performance.js", "setup:test-users": "node scripts/setup-test-users.js", "setup:redis-credits": "node src/scripts/init-redis-credits.js", "fix:database": "node scripts/fix-database-issues.js", "db:migrate": "node src/scripts/migrate.js", "docs:open": "open http://localhost:3001/docs || xdg-open http://localhost:3001/docs || start http://localhost:3001/docs", "docs:validate": "node scripts/update-openapi-json.js"}, "keywords": ["api", "websocket", "redis", "postgresql", "credit-system"], "author": "", "license": "ISC", "packageManager": "pnpm@10.10.0", "dependencies": {"@scalar/api-reference": "^1.31.2", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "ioredis": "^5.6.1", "kafkajs": "^2.2.4", "morgan": "^1.10.0", "pg": "^8.16.0", "uuid": "^11.1.0", "ws": "^8.18.2"}}